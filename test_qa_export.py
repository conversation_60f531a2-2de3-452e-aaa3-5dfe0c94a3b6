#!/usr/bin/env python3
"""
Test script for Q&A export functionality.
"""

import os
import sys
import json
import logging
from dotenv import load_dotenv

# Add the current directory to the path so we can import the exporter
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from export_qa_from_database import QAExporter

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_export_functionality():
    """Test the Q&A export functionality."""
    
    exporter = QAExporter()
    
    try:
        # Connect to database
        exporter.connect()
        logger.info("✅ Database connection successful")
        
        # Test getting available filters
        logger.info("\n=== Testing Filter Discovery ===")
        filters = exporter.get_available_filters()
        
        logger.info(f"Found {len(filters['generation_types'])} generation types")
        logger.info(f"Found {len(filters['generation_params'])} generation parameter sets")
        logger.info(f"Found {len(filters['models'])} models")
        
        # Show available options
        for gt in filters['generation_types']:
            logger.info(f"  Generation Type: {gt['name']} (ID: {gt['id']})")
        
        for gp in filters['generation_params'][:3]:  # Show first 3
            logger.info(f"  Generation Params ID {gp['id']}: {gp['usage_count']} records")
        
        # Test fetching a small sample of data
        logger.info("\n=== Testing Data Fetch ===")
        qa_records = exporter.fetch_qa_data(limit=5)
        
        if qa_records:
            logger.info(f"✅ Successfully fetched {len(qa_records)} Q&A records")
            
            # Show sample record
            sample = qa_records[0]
            logger.info(f"Sample record:")
            logger.info(f"  Question: {sample['question'][:80]}...")
            logger.info(f"  Answer: {sample['answer'][:80]}...")
            logger.info(f"  Source: {sample['document_title'][:60]}...")
            
            # Test different export formats
            logger.info("\n=== Testing Export Formats ===")
            
            formats = ['phi3', 'openai', 'simple']
            for format_type in formats:
                test_file = f"test_export_{format_type}.jsonl"
                
                try:
                    exporter.export_to_jsonl(qa_records, test_file, format_type)
                    
                    # Verify the file was created and has correct format
                    with open(test_file, 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                        data = json.loads(first_line)
                        
                        if format_type == 'phi3':
                            assert 'text' in data
                            assert '<|user|>' in data['text']
                            assert '<|assistant|>' in data['text']
                        elif format_type == 'openai':
                            assert 'messages' in data
                            assert len(data['messages']) == 2
                            assert data['messages'][0]['role'] == 'user'
                            assert data['messages'][1]['role'] == 'assistant'
                        elif format_type == 'simple':
                            assert 'prompt' in data
                            assert 'completion' in data
                    
                    logger.info(f"✅ {format_type} format export successful")
                    
                    # Clean up test file
                    os.remove(test_file)
                    
                except Exception as e:
                    logger.error(f"❌ {format_type} format export failed: {e}")
                    return False
            
            logger.info("✅ All export format tests passed")
            return True
            
        else:
            logger.warning("⚠️ No Q&A records found in database")
            return False
        
    except Exception as e:
        logger.error(f"❌ Export test failed: {e}")
        return False
    finally:
        exporter.disconnect()

def test_filtered_export():
    """Test export with filters."""
    
    exporter = QAExporter()
    
    try:
        exporter.connect()
        
        logger.info("\n=== Testing Filtered Export ===")
        
        # Get available filters
        filters = exporter.get_available_filters()
        
        if filters['generation_params']:
            # Test filtering by generation_params_id
            params_id = filters['generation_params'][0]['id']
            logger.info(f"Testing filter by generation_params_id: {params_id}")
            
            filtered_records = exporter.fetch_qa_data(limit=10, generation_params_id=params_id)
            logger.info(f"✅ Filtered export returned {len(filtered_records)} records")
            
            # Verify all records have the correct generation_params_id
            for record in filtered_records:
                # We can't directly verify this from the returned data, but the query should work
                pass
            
            return True
        else:
            logger.warning("⚠️ No generation parameters found for filter testing")
            return False
        
    except Exception as e:
        logger.error(f"❌ Filtered export test failed: {e}")
        return False
    finally:
        exporter.disconnect()

if __name__ == "__main__":
    logger.info("Starting Q&A export tests...")
    
    # Test 1: Basic functionality
    logger.info("\n=== Test 1: Basic Export Functionality ===")
    basic_ok = test_export_functionality()
    
    # Test 2: Filtered export
    logger.info("\n=== Test 2: Filtered Export ===")
    filtered_ok = test_filtered_export()
    
    # Summary
    if basic_ok and filtered_ok:
        logger.info("\n🎉 All Q&A export tests PASSED!")
        logger.info("\nYou can now use:")
        logger.info("  python export_qa_from_database.py --limit 100 --format phi3")
        logger.info("  python export_qa_from_database.py --list-filters")
        logger.info("  python export_qa_from_database.py --generation-params-id 1 --limit 50")
    else:
        logger.error("\n💥 Some tests FAILED!")
        sys.exit(1)
