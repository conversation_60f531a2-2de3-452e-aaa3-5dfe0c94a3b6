#!/usr/bin/env python3
"""
Convert Q/A/S text files to MLX chat format for training.

This script converts text files with the format:
Q: (the user prompt)
A: (the completion)
S: the source of information

To the MLX chat format:
{"messages": [
    {"role": "system", "content": "You are ..."}, 
    {"role": "user", "content": "the user prompt is ..."}, 
    {"role": "assistant", "content": "the answer is ..."}
]}

If a .systemprompt file exists with the same base name, it will be included as the system message.
This format is model-agnostic and mlx_lm will automatically apply the appropriate model templates.
"""

import json
import argparse
import sys
import re
from pathlib import Path
from typing import List, Dict, Optional, Tuple


def parse_qas_text(text: str) -> List[Tuple[str, str, str]]:
    """
    Parse Q/A/S format text into tuples of (question, answer, source).
    
    Args:
        text: The input text containing Q/A/S entries
        
    Returns:
        List of (question, answer, source) tuples
    """
    entries = []
    lines = text.strip().split('\n')
    
    current_q = None
    current_a = None
    current_s = None
    
    for line in lines:
        line = line.strip()
        if not line:
            # Empty line - if we have a complete Q/A/S, save it
            if current_q and current_a and current_s:
                entries.append((current_q, current_a, current_s))
                current_q = current_a = current_s = None
            continue
            
        if line.startswith('Q: '):
            current_q = line[3:].strip()
        elif line.startswith('A: '):
            current_a = line[3:].strip()
        elif line.startswith('S: '):
            current_s = line[3:].strip()
    
    # Don't forget the last entry if file doesn't end with empty line
    if current_q and current_a and current_s:
        entries.append((current_q, current_a, current_s))
    
    return entries


def load_system_prompt(input_file: Path) -> Optional[str]:
    """
    Load system prompt from .systemprompt file if it exists.
    
    Args:
        input_file: Path to the input text file
        
    Returns:
        System prompt content or None if file doesn't exist
    """
    system_prompt_file = input_file.with_suffix('.systemprompt')
    
    if system_prompt_file.exists():
        try:
            with open(system_prompt_file, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception as e:
            print(f"Warning: Could not read system prompt file {system_prompt_file}: {e}")
            return None
    
    return None


def convert_to_mlx_chat_format(question: str, answer: str, source: str, system_prompt: Optional[str] = None) -> Dict:
    """
    Convert Q/A/S to MLX chat format.

    Args:
        question: The user question
        answer: The assistant answer
        source: The source information (not used in chat format but preserved in metadata)
        system_prompt: Optional system prompt

    Returns:
        Dictionary in MLX chat format
    """
    messages = []
    
    # Add system message if provided
    if system_prompt:
        messages.append({
            "role": "system",
            "content": system_prompt
        })
    
    # Add user message
    messages.append({
        "role": "user", 
        "content": question
    })
    
    # Add assistant message
    messages.append({
        "role": "assistant",
        "content": answer
    })
    
    return {"messages": messages}


def convert_text_file(input_file: Path, output_file: Path) -> None:
    """
    Convert a Q/A/S text file to MLX chat format JSONL.
    
    Args:
        input_file: Path to the input text file
        output_file: Path to the output JSONL file
    """
    converted_count = 0
    skipped_count = 0
    
    try:
        # Load the input text file
        with open(input_file, 'r', encoding='utf-8') as f:
            text_content = f.read()
        
        # Load system prompt if available
        system_prompt = load_system_prompt(input_file)
        if system_prompt:
            print(f"Using system prompt from {input_file.with_suffix('.systemprompt')}")
        
        # Parse Q/A/S entries
        entries = parse_qas_text(text_content)
        
        if not entries:
            print(f"Warning: No Q/A/S entries found in {input_file}")
            return
        
        # Convert and write to output file
        with open(output_file, 'w', encoding='utf-8') as outfile:
            for question, answer, source in entries:
                try:
                    # Convert to MLX chat format
                    chat_item = convert_to_mlx_chat_format(question, answer, source, system_prompt)
                    
                    # Write to output file
                    outfile.write(json.dumps(chat_item, ensure_ascii=False) + '\n')
                    converted_count += 1
                    
                except Exception as e:
                    print(f"Warning: Error processing Q/A/S entry - {e}, skipping")
                    skipped_count += 1
                    continue
        
        print(f"Conversion complete!")
        print(f"  Converted: {converted_count} entries")
        print(f"  Skipped: {skipped_count} entries")
        print(f"  Output file: {output_file}")
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error during conversion: {e}")
        sys.exit(1)


def preview_conversion(input_file: Path, num_examples: int = 3) -> None:
    """
    Preview the conversion by showing a few examples.
    
    Args:
        input_file: Path to the input text file
        num_examples: Number of examples to show
    """
    print(f"Preview of conversion from {input_file}:")
    print("=" * 60)
    
    try:
        # Load the input text file
        with open(input_file, 'r', encoding='utf-8') as f:
            text_content = f.read()
        
        # Load system prompt if available
        system_prompt = load_system_prompt(input_file)
        
        # Parse Q/A/S entries
        entries = parse_qas_text(text_content)
        
        if not entries:
            print(f"No Q/A/S entries found in {input_file}")
            return
        
        for i, (question, answer, source) in enumerate(entries[:num_examples], 1):
            print(f"\nExample {i}:")
            print(f"Q: {question[:100]}{'...' if len(question) > 100 else ''}")
            print(f"A: {answer[:100]}{'...' if len(answer) > 100 else ''}")
            print(f"S: {source}")
            
            chat_item = convert_to_mlx_chat_format(question, answer, source, system_prompt)
            print(f"MLX Chat format:")
            print(json.dumps(chat_item, indent=2, ensure_ascii=False)[:300] + "...")
            print("-" * 40)
                    
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found")
        sys.exit(1)


def main():
    """Main function to handle command line arguments and execute conversion."""
    parser = argparse.ArgumentParser(
        description="Convert Q/A/S text files to MLX chat format for training",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Convert a Q/A/S text file
  python qastxt2mlxchat.py input.txt output.jsonl
  
  # Preview conversion without creating output file
  python qastxt2mlxchat.py input.txt --preview
  
  # Preview with more examples
  python qastxt2mlxchat.py input.txt --preview --examples 5

Input format:
  Q: What is the capital of France?
  A: The capital of France is Paris.
  S: Geography textbook

  Q: What is 2+2?
  A: 2+2 equals 4.
  S: Mathematics

Optional system prompt:
  Create a file with the same name but .systemprompt extension
  (e.g., input.systemprompt) containing the system prompt.
        """
    )
    
    parser.add_argument(
        'input_file',
        type=Path,
        help='Input text file with Q/A/S format'
    )
    parser.add_argument(
        'output_file',
        type=Path,
        nargs='?',
        help='Output JSONL file in MLX chat format (required unless --preview is used)'
    )
    parser.add_argument(
        '--preview',
        action='store_true',
        help='Preview the conversion without creating output file'
    )
    parser.add_argument(
        '--examples',
        type=int,
        default=3,
        help='Number of examples to show in preview mode (default: 3)'
    )
    
    args = parser.parse_args()
    
    # Validate input file exists
    if not args.input_file.exists():
        print(f"Error: Input file '{args.input_file}' does not exist")
        sys.exit(1)
    
    # Validate input file has .txt extension
    if args.input_file.suffix.lower() != '.txt':
        print(f"Warning: Input file '{args.input_file}' does not have .txt extension")
    
    if args.preview:
        # Preview mode
        preview_conversion(args.input_file, args.examples)
    else:
        # Conversion mode
        if not args.output_file:
            print("Error: Output file is required unless --preview is used")
            parser.print_help()
            sys.exit(1)
        
        # Create output directory if it doesn't exist
        args.output_file.parent.mkdir(parents=True, exist_ok=True)
        
        convert_text_file(args.input_file, args.output_file)


if __name__ == "__main__":
    main()
