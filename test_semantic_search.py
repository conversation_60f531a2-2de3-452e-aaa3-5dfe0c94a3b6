#!/usr/bin/env python3
"""
Test script for semantic search functionality.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from semantic_search import SemanticSearcher

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_database_connection():
    """Test database connection and schema validation."""
    
    searcher = SemanticSearcher()
    
    try:
        searcher.connect()
        logger.info("✅ Database connection successful")
        
        # Check if required tables exist
        tables_to_check = ['document', 'chunk', 'emb_1024']
        
        for table in tables_to_check:
            searcher.cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """, (table,))
            
            exists = searcher.cursor.fetchone()[0]
            if exists:
                logger.info(f"✅ Table '{table}' exists")
            else:
                logger.error(f"❌ Table '{table}' does not exist")
                return False
        
        # Check if pgvector extension is available
        searcher.cursor.execute("SELECT 1 FROM pg_extension WHERE extname = 'vector';")
        if searcher.cursor.fetchone():
            logger.info("✅ pgvector extension is available")
        else:
            logger.warning("⚠️ pgvector extension not found")
        
        # Get database statistics
        stats = searcher.get_database_stats()
        logger.info(f"📊 Database stats: {stats}")
        
        if stats.get('total_embeddings', 0) == 0:
            logger.warning("⚠️ No embeddings found in database")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connection test failed: {e}")
        return False
    finally:
        searcher.disconnect()

def test_embedding_generation():
    """Test embedding generation with Ollama."""
    
    searcher = SemanticSearcher()
    
    test_questions = [
        "What is the treatment for heart failure?",
        "How is diabetes diagnosed?",
        "What are the symptoms of stroke?"
    ]
    
    logger.info("=== Testing Embedding Generation ===")
    
    for question in test_questions:
        try:
            logger.info(f"Testing: {question}")
            embedding = searcher.get_question_embedding(question)
            
            if embedding and len(embedding) > 0:
                logger.info(f"✅ Generated embedding with {len(embedding)} dimensions")
                
                # Check if embedding values are reasonable
                if all(isinstance(x, (int, float)) for x in embedding):
                    logger.info("✅ Embedding values are numeric")
                else:
                    logger.error("❌ Embedding contains non-numeric values")
                    return False
            else:
                logger.error("❌ Empty embedding generated")
                return False
                
        except Exception as e:
            logger.error(f"❌ Embedding generation failed for '{question}': {e}")
            return False
    
    return True

def test_semantic_search():
    """Test the complete semantic search functionality."""
    
    searcher = SemanticSearcher()
    
    test_cases = [
        {
            "question": "heart failure treatment",
            "expected_terms": ["heart", "failure", "treatment", "cardiac"]
        },
        {
            "question": "diabetes diagnosis blood sugar",
            "expected_terms": ["diabetes", "diagnosis", "glucose", "blood"]
        },
        {
            "question": "stroke symptoms neurological",
            "expected_terms": ["stroke", "symptoms", "neurological", "brain"]
        }
    ]
    
    logger.info("=== Testing Semantic Search ===")
    
    try:
        searcher.connect()
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"\nTest case {i}: {test_case['question']}")
            
            # Generate embedding
            embedding = searcher.get_question_embedding(test_case['question'])
            
            # Search for similar chunks
            results = searcher.search_similar_chunks(embedding, limit=5)
            
            if results:
                logger.info(f"✅ Found {len(results)} results")
                
                # Check similarity scores
                best_score = results[0]['similarity_score']
                logger.info(f"✅ Best similarity score: {best_score:.4f}")
                
                if best_score > 0.1:  # Reasonable similarity threshold
                    logger.info("✅ Similarity scores look reasonable")
                else:
                    logger.warning(f"⚠️ Low similarity scores (best: {best_score:.4f})")
                
                # Show top result
                top_result = results[0]
                logger.info(f"Top result: Doc {top_result['document_id']} - {top_result['document_title'][:60]}...")
                
            else:
                logger.error("❌ No results found")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Semantic search test failed: {e}")
        return False
    finally:
        searcher.disconnect()

def test_edge_cases():
    """Test edge cases and error handling."""
    
    searcher = SemanticSearcher()
    
    logger.info("=== Testing Edge Cases ===")
    
    edge_cases = [
        "",  # Empty string
        "a",  # Single character
        "x" * 1000,  # Very long string
        "!@#$%^&*()",  # Special characters only
        "What is the meaning of life, the universe, and everything?"  # Long question
    ]
    
    try:
        searcher.connect()
        
        for i, test_input in enumerate(edge_cases, 1):
            logger.info(f"\nEdge case {i}: '{test_input[:50]}{'...' if len(test_input) > 50 else ''}'")
            
            try:
                if test_input.strip():  # Skip empty strings for embedding
                    embedding = searcher.get_question_embedding(test_input)
                    results = searcher.search_similar_chunks(embedding, limit=3)
                    logger.info(f"✅ Handled edge case - got {len(results)} results")
                else:
                    logger.info("⚠️ Skipped empty string")
                    
            except Exception as e:
                logger.warning(f"⚠️ Edge case failed (expected): {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Edge case testing failed: {e}")
        return False
    finally:
        searcher.disconnect()

def test_performance():
    """Test performance with multiple searches."""
    
    searcher = SemanticSearcher()
    
    logger.info("=== Testing Performance ===")
    
    import time
    
    test_questions = [
        "cardiovascular disease risk factors",
        "antibiotic resistance mechanisms",
        "cancer immunotherapy effectiveness",
        "neurological disorders diagnosis",
        "respiratory infection treatment"
    ]
    
    try:
        searcher.connect()
        
        total_time = 0
        successful_searches = 0
        
        for question in test_questions:
            start_time = time.time()
            
            try:
                embedding = searcher.get_question_embedding(question)
                results = searcher.search_similar_chunks(embedding, limit=10)
                
                end_time = time.time()
                search_time = end_time - start_time
                total_time += search_time
                successful_searches += 1
                
                logger.info(f"✅ '{question}' - {len(results)} results in {search_time:.2f}s")
                
            except Exception as e:
                logger.error(f"❌ Search failed for '{question}': {e}")
        
        if successful_searches > 0:
            avg_time = total_time / successful_searches
            logger.info(f"\n📊 Performance Summary:")
            logger.info(f"   Successful searches: {successful_searches}/{len(test_questions)}")
            logger.info(f"   Average search time: {avg_time:.2f}s")
            logger.info(f"   Total time: {total_time:.2f}s")
            
            return True
        else:
            logger.error("❌ No successful searches")
            return False
        
    except Exception as e:
        logger.error(f"❌ Performance test failed: {e}")
        return False
    finally:
        searcher.disconnect()

if __name__ == "__main__":
    logger.info("Starting semantic search tests...")
    
    # Test 1: Database connection
    logger.info("\n=== Test 1: Database Connection ===")
    db_ok = test_database_connection()
    
    if not db_ok:
        logger.error("💥 Database connection failed - stopping tests")
        sys.exit(1)
    
    # Test 2: Embedding generation
    logger.info("\n=== Test 2: Embedding Generation ===")
    embedding_ok = test_embedding_generation()
    
    if not embedding_ok:
        logger.error("💥 Embedding generation failed - stopping tests")
        sys.exit(1)
    
    # Test 3: Semantic search
    logger.info("\n=== Test 3: Semantic Search ===")
    search_ok = test_semantic_search()
    
    # Test 4: Edge cases
    logger.info("\n=== Test 4: Edge Cases ===")
    edge_ok = test_edge_cases()
    
    # Test 5: Performance
    logger.info("\n=== Test 5: Performance ===")
    perf_ok = test_performance()
    
    # Summary
    if db_ok and embedding_ok and search_ok and edge_ok and perf_ok:
        logger.info("\n🎉 All semantic search tests PASSED!")
        logger.info("\nYou can now use:")
        logger.info("  python semantic_search.py 'What is the treatment for heart failure?'")
        logger.info("  python semantic_search.py 'diabetes diagnosis' --limit 10 --show-text")
    else:
        logger.error("\n💥 Some tests FAILED!")
        sys.exit(1)
