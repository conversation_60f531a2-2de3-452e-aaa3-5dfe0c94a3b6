#!/usr/bin/env python3
"""
Simple Vector Search - Minimal and Fast

A simplified version of semantic search focused on performance and reliability.
"""

import os
import sys
import logging
import argparse
import time
from typing import List, Dict
import psycopg2
from psycopg2 import Error
from dotenv import load_dotenv
import ollama

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class SimpleVectorSearch:
    """Simple, reliable vector search."""

    def __init__(self, embedding_model: str = "snowflake-arctic-embed2:latest"):
        self.embedding_model = embedding_model
        self.connection = None
        self.cursor = None
        
        # Database connection
        self.db_config = {
            'host': os.getenv('POSTGRES_HOST'),
            'port': os.getenv('POSTGRES_PORT', 5432),
            'database': os.getenv('POSTGRES_DB'),
            'user': os.getenv('POSTGRES_USER'),
            'password': os.getenv('POSTGRES_PASSWORD')
        }

    def connect(self):
        """Simple database connection."""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info("Connected to database")
        except Error as e:
            logger.error(f"Connection error: {e}")
            raise

    def disconnect(self):
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()

    def get_embedding(self, text: str) -> List[float]:
        """Get embedding from Ollama."""
        try:
            logger.info(f"Getting embedding for: {text[:50]}...")
            response = ollama.embeddings(model=self.embedding_model, prompt=text)
            embedding = response['embedding']
            logger.info(f"Generated {len(embedding)}-dimensional embedding")
            return embedding
        except Exception as e:
            logger.error(f"Embedding error: {e}")
            raise

    def search_vector_only(self, question: str, limit: int = 20) -> List[Dict]:
        """Fast vector-only search."""
        
        start_time = time.time()
        
        # Get embedding
        embedding = self.get_embedding(question)
        embedding_str = '[' + ','.join(map(str, embedding)) + ']'
        
        # Simple vector search
        query = """
            SELECT 
                chunk_id,
                embedding <=> %s::vector as distance
            FROM emb_1024
            ORDER BY embedding <=> %s::vector
            LIMIT %s;
        """
        
        logger.info(f"Searching for {limit} most similar chunks...")
        self.cursor.execute(query, (embedding_str, embedding_str, limit))
        results = self.cursor.fetchall()
        
        search_time = time.time() - start_time
        logger.info(f"Vector search completed in {search_time:.3f}s")
        
        # Format results
        formatted_results = []
        for row in results:
            chunk_id, distance = row
            formatted_results.append({
                'chunk_id': chunk_id,
                'distance': float(distance),
                'similarity_score': float(1 - distance)
            })
        
        return formatted_results

    def search_with_metadata(self, question: str, limit: int = 20) -> List[Dict]:
        """Two-stage search: vector first, then metadata."""
        
        start_time = time.time()
        
        # Stage 1: Get top chunk IDs
        vector_results = self.search_vector_only(question, limit)
        
        if not vector_results:
            return []
        
        # Stage 2: Get metadata for these chunks
        chunk_ids = [r['chunk_id'] for r in vector_results]
        distance_map = {r['chunk_id']: r for r in vector_results}
        
        # Format chunk IDs for SQL IN clause
        chunk_ids_str = ','.join(map(str, chunk_ids))
        
        metadata_query = f"""
            SELECT 
                c.id as chunk_id,
                c.document_id,
                d.title as document_title,
                c.text as chunk_text
            FROM chunks c
            JOIN document d ON c.document_id = d.id
            WHERE c.id IN ({chunk_ids_str})
        """
        
        logger.info("Getting metadata for top chunks...")
        self.cursor.execute(metadata_query)
        metadata_results = self.cursor.fetchall()
        
        # Combine results
        final_results = []
        metadata_dict = {}
        
        for row in metadata_results:
            chunk_id, document_id, document_title, chunk_text = row
            metadata_dict[chunk_id] = {
                'document_id': document_id,
                'document_title': document_title,
                'chunk_text': chunk_text
            }
        
        # Maintain original order and add metadata
        for chunk_id in chunk_ids:
            if chunk_id in metadata_dict:
                vector_data = distance_map[chunk_id]
                metadata = metadata_dict[chunk_id]
                
                final_results.append({
                    'chunk_id': chunk_id,
                    'document_id': metadata['document_id'],
                    'document_title': metadata['document_title'],
                    'chunk_text': metadata['chunk_text'],
                    'distance': vector_data['distance'],
                    'similarity_score': vector_data['similarity_score']
                })
        
        total_time = time.time() - start_time
        logger.info(f"Full search completed in {total_time:.3f}s")
        
        return final_results

    def check_index(self):
        """Check if vector index exists."""
        try:
            index_query = """
                SELECT indexname, indexdef
                FROM pg_indexes 
                WHERE tablename = 'emb_1024'
                AND indexdef ILIKE '%hnsw%';
            """
            
            self.cursor.execute(index_query)
            indexes = self.cursor.fetchall()
            
            if indexes:
                logger.info(f"Found {len(indexes)} HNSW index(es):")
                for idx_name, idx_def in indexes:
                    logger.info(f"  {idx_name}")
                return True
            else:
                logger.warning("No HNSW index found on emb_1024.embedding")
                logger.info("Consider creating: CREATE INDEX ON emb_1024 USING hnsw (embedding vector_cosine_ops);")
                return False
                
        except Error as e:
            logger.error(f"Error checking index: {e}")
            return False

    def get_stats(self):
        """Get basic database statistics."""
        try:
            # Count embeddings
            self.cursor.execute("SELECT COUNT(*) FROM emb_1024;")
            embedding_count = self.cursor.fetchone()[0]
            
            # Count documents
            self.cursor.execute("SELECT COUNT(*) FROM document;")
            doc_count = self.cursor.fetchone()[0]
            
            # Count chunks
            self.cursor.execute("SELECT COUNT(*) FROM chunks;")
            chunk_count = self.cursor.fetchone()[0]
            
            logger.info(f"Database stats: {embedding_count:,} embeddings, {chunk_count:,} chunks, {doc_count:,} documents")
            
        except Error as e:
            logger.error(f"Error getting stats: {e}")

def main():
    parser = argparse.ArgumentParser(description='Simple vector search')
    parser.add_argument('question', type=str, help='Search question')
    parser.add_argument('--limit', type=int, default=20, help='Number of results')
    parser.add_argument('--vector-only', action='store_true', help='Vector search only (no metadata)')
    parser.add_argument('--check-index', action='store_true', help='Check if HNSW index exists')
    parser.add_argument('--stats', action='store_true', help='Show database statistics')
    parser.add_argument('--show-text', action='store_true', help='Show chunk text in results')
    
    args = parser.parse_args()
    
    searcher = SimpleVectorSearch()
    
    try:
        searcher.connect()
        
        # Show stats if requested
        if args.stats:
            searcher.get_stats()
        
        # Check index if requested
        if args.check_index:
            searcher.check_index()
        
        # Perform search
        if args.vector_only:
            results = searcher.search_vector_only(args.question, args.limit)
            
            print(f"\n{'='*60}")
            print(f"VECTOR-ONLY SEARCH RESULTS ({len(results)})")
            print(f"{'='*60}")
            
            for i, result in enumerate(results, 1):
                print(f"{i:2d}. Chunk ID: {result['chunk_id']} | "
                      f"Similarity: {result['similarity_score']:.4f} | "
                      f"Distance: {result['distance']:.6f}")
        
        else:
            results = searcher.search_with_metadata(args.question, args.limit)
            
            print(f"\n{'='*60}")
            print(f"SEARCH RESULTS ({len(results)})")
            print(f"{'='*60}")
            
            for i, result in enumerate(results, 1):
                print(f"\n{i:2d}. Document ID: {result['document_id']} | "
                      f"Similarity: {result['similarity_score']:.4f}")
                print(f"    Title: {result['document_title']}")
                
                if args.show_text and result['chunk_text']:
                    # Show first 200 characters
                    text_preview = result['chunk_text'][:200].replace('\n', ' ')
                    print(f"    Text: {text_preview}...")
        
        # Summary
        if results:
            best_score = max(r['similarity_score'] for r in results)
            worst_score = min(r['similarity_score'] for r in results)
            print(f"\nSimilarity range: {worst_score:.4f} - {best_score:.4f}")
            
            if not args.vector_only:
                unique_docs = len(set(r['document_id'] for r in results))
                print(f"Unique documents: {unique_docs}")
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        sys.exit(1)
    finally:
        searcher.disconnect()

if __name__ == "__main__":
    main()
