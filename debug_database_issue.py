#!/usr/bin/env python3
"""
Debug script to investigate the missing generated_data records issue.
"""

import os
import sys
import json
import logging
import psycopg2
from psycopg2 import sql, Error
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def check_database_state():
    """Check the current state of the database tables."""
    
    # Get connection parameters from environment
    db_config = {
        'host': os.getenv('POSTGRES_HOST'),
        'port': os.getenv('POSTGRES_PORT', 5432),
        'database': os.getenv('POSTGRES_DB'),
        'user': os.getenv('POSTGRES_USER'),
        'password': os.getenv('POSTGRES_PASSWORD')
    }
    
    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()
        
        logger.info("=== Database State Check ===")
        
        # Check generation_params table
        cursor.execute("SELECT COUNT(*) FROM generation_params;")
        gen_params_count = cursor.fetchone()[0]
        logger.info(f"generation_params records: {gen_params_count}")
        
        if gen_params_count > 0:
            cursor.execute("SELECT id, system_prompt[:50], generation_prompt[:50] FROM generation_params ORDER BY id DESC LIMIT 3;")
            for row in cursor.fetchall():
                logger.info(f"  ID {row[0]}: {row[1]}... | {row[2]}...")
        
        # Check generated_type table
        cursor.execute("SELECT COUNT(*) FROM generated_type;")
        gen_type_count = cursor.fetchone()[0]
        logger.info(f"generated_type records: {gen_type_count}")
        
        cursor.execute("SELECT id, type_name, description FROM generated_type;")
        for row in cursor.fetchall():
            logger.info(f"  ID {row[0]}: {row[1]} - {row[2]}")
        
        # Check generated_data table
        cursor.execute("SELECT COUNT(*) FROM generated_data;")
        gen_data_count = cursor.fetchone()[0]
        logger.info(f"generated_data records: {gen_data_count}")
        
        if gen_data_count > 0:
            cursor.execute("""
                SELECT gd.id, gd.document_id, gd.generation_params_id, gd.generation_type, 
                       LEFT(gd.data, 100) as data_preview
                FROM generated_data gd 
                ORDER BY gd.id DESC 
                LIMIT 5;
            """)
            for row in cursor.fetchall():
                logger.info(f"  ID {row[0]}: doc_id={row[1]}, params_id={row[2]}, type={row[3]}")
                logger.info(f"    Data: {row[4]}...")
        
        # Check if there are any recent transactions that might not be committed
        cursor.execute("SELECT txid_current();")
        current_txid = cursor.fetchone()[0]
        logger.info(f"Current transaction ID: {current_txid}")
        
        # Check table permissions
        cursor.execute("""
            SELECT table_name, privilege_type 
            FROM information_schema.role_table_grants 
            WHERE grantee = current_user 
            AND table_name IN ('generated_data', 'generation_params', 'generated_type')
            ORDER BY table_name, privilege_type;
        """)
        
        logger.info("Table permissions for current user:")
        for row in cursor.fetchall():
            logger.info(f"  {row[0]}: {row[1]}")
        
        connection.close()
        
    except Exception as e:
        logger.error(f"Error checking database state: {e}")

def test_manual_insert():
    """Test manually inserting a record to see if it works."""
    
    db_config = {
        'host': os.getenv('POSTGRES_HOST'),
        'port': os.getenv('POSTGRES_PORT', 5432),
        'database': os.getenv('POSTGRES_DB'),
        'user': os.getenv('POSTGRES_USER'),
        'password': os.getenv('POSTGRES_PASSWORD')
    }
    
    try:
        connection = psycopg2.connect(**db_config)
        cursor = connection.cursor()
        
        logger.info("\n=== Manual Insert Test ===")
        
        # Get a test document ID
        cursor.execute("SELECT id FROM document LIMIT 1;")
        doc_result = cursor.fetchone()
        if not doc_result:
            logger.error("No documents found in database")
            return
        
        test_doc_id = doc_result[0]
        logger.info(f"Using test document ID: {test_doc_id}")
        
        # Get generation_params ID
        cursor.execute("SELECT id FROM generation_params ORDER BY id DESC LIMIT 1;")
        params_result = cursor.fetchone()
        if not params_result:
            logger.error("No generation_params found")
            return
        
        test_params_id = params_result[0]
        logger.info(f"Using generation_params ID: {test_params_id}")
        
        # Get AQA type ID
        cursor.execute("SELECT id FROM generated_type WHERE type_name = 'AQA';")
        type_result = cursor.fetchone()
        if not type_result:
            logger.error("AQA type not found")
            return
        
        aqa_type_id = type_result[0]
        logger.info(f"Using AQA type ID: {aqa_type_id}")
        
        # Create test data
        test_data = json.dumps({
            "question": "Test question for debugging",
            "answer": "Test answer for debugging"
        }, ensure_ascii=False)
        
        # Insert test record
        insert_query = """
            INSERT INTO generated_data (document_id, generation_params_id, generation_type, data)
            VALUES (%s, %s, %s, %s)
            RETURNING id;
        """
        
        cursor.execute(insert_query, (test_doc_id, test_params_id, aqa_type_id, test_data))
        new_id = cursor.fetchone()[0]
        
        logger.info(f"Inserted test record with ID: {new_id}")
        
        # Commit the transaction
        connection.commit()
        logger.info("Transaction committed successfully")
        
        # Verify the record exists
        cursor.execute("SELECT COUNT(*) FROM generated_data WHERE id = %s;", (new_id,))
        count = cursor.fetchone()[0]
        
        if count == 1:
            logger.info("✅ Test record successfully inserted and verified")
        else:
            logger.error("❌ Test record not found after insert")
        
        # Clean up test record
        cursor.execute("DELETE FROM generated_data WHERE id = %s;", (new_id,))
        connection.commit()
        logger.info("Test record cleaned up")
        
        connection.close()
        
    except Exception as e:
        logger.error(f"Error in manual insert test: {e}")
        if 'connection' in locals():
            connection.rollback()
            connection.close()

def check_autocommit_setting():
    """Check if autocommit is enabled."""
    
    db_config = {
        'host': os.getenv('POSTGRES_HOST'),
        'port': os.getenv('POSTGRES_PORT', 5432),
        'database': os.getenv('POSTGRES_DB'),
        'user': os.getenv('POSTGRES_USER'),
        'password': os.getenv('POSTGRES_PASSWORD')
    }
    
    try:
        connection = psycopg2.connect(**db_config)
        
        logger.info(f"\n=== Connection Settings ===")
        logger.info(f"Autocommit: {connection.autocommit}")
        logger.info(f"Isolation level: {connection.isolation_level}")
        
        connection.close()
        
    except Exception as e:
        logger.error(f"Error checking connection settings: {e}")

if __name__ == "__main__":
    logger.info("Starting database debugging...")
    
    # Check current database state
    check_database_state()
    
    # Check connection settings
    check_autocommit_setting()
    
    # Test manual insert
    test_manual_insert()
    
    logger.info("\nDebugging complete!")
    logger.info("\nIf the manual insert test worked, the issue might be:")
    logger.info("1. Transaction not being committed in the main script")
    logger.info("2. Connection being closed before commit")
    logger.info("3. Error in the abstract_qa.py logic")
