#!/usr/bin/env python3
"""
Export Q&A Data from Database

This script fetches Q&A pairs from the generated_data table and exports them
to JSONL format in chat message format for machine learning training.
"""

import os
import json
import logging
import argparse
from typing import List, Dict, Optional
import psycopg2
from psycopg2 import sql, Error
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'export_qa_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class QAExporter:
    """
    Exports Q&A pairs from the database to JSONL format.
    """

    def __init__(self):
        """Initialize database connection using environment variables."""
        self.connection = None
        self.cursor = None
        
        # Get connection parameters from environment
        self.db_config = {
            'host': os.getenv('POSTGRES_HOST'),
            'port': os.getenv('POSTGRES_PORT', 5432),
            'database': os.getenv('POSTGRES_DB'),
            'user': os.getenv('POSTGRES_USER'),
            'password': os.getenv('POSTGRES_PASSWORD')
        }
        
        # Validate required environment variables
        required_vars = ['POSTGRES_HOST', 'POSTGRES_DB', 'POSTGRES_USER', 'POSTGRES_PASSWORD']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

    def connect(self):
        """Establish database connection."""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info(f"Successfully connected to PostgreSQL database: {self.db_config['database']}")
            
        except Error as e:
            logger.error(f"Error connecting to PostgreSQL database: {e}")
            raise
    
    def disconnect(self):
        """Close database connection."""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("Database connection closed")

    def get_available_filters(self) -> Dict[str, List]:
        """
        Get available filter options from the database.
        
        Returns:
            dict: Available models, generation types, and generation params
        """
        try:
            filters = {}
            
            # Get available generation types
            self.cursor.execute("SELECT id, type_name, description FROM generated_type ORDER BY type_name;")
            filters['generation_types'] = [
                {'id': row[0], 'name': row[1], 'description': row[2]} 
                for row in self.cursor.fetchall()
            ]
            
            # Get available generation parameters (with counts)
            self.cursor.execute("""
                SELECT gp.id, gp.system_prompt, gp.generation_prompt, COUNT(gd.id) as usage_count
                FROM generation_params gp
                LEFT JOIN generated_data gd ON gp.id = gd.generation_params_id
                GROUP BY gp.id, gp.system_prompt, gp.generation_prompt
                ORDER BY usage_count DESC, gp.id DESC;
            """)
            filters['generation_params'] = [
                {
                    'id': row[0], 
                    'system_prompt': row[1][:100] + '...' if len(row[1]) > 100 else row[1],
                    'generation_prompt': row[2][:100] + '...' if len(row[2]) > 100 else row[2],
                    'usage_count': row[3]
                } 
                for row in self.cursor.fetchall()
            ]
            
            # Get available models (if models table exists and is referenced)
            try:
                self.cursor.execute("""
                    SELECT DISTINCT m.id, m.name, COUNT(gd.id) as usage_count
                    FROM models m
                    JOIN generation_params gp ON m.id = gp.model_id
                    JOIN generated_data gd ON gp.id = gd.generation_params_id
                    GROUP BY m.id, m.name
                    ORDER BY usage_count DESC;
                """)
                filters['models'] = [
                    {'id': row[0], 'name': row[1], 'usage_count': row[2]} 
                    for row in self.cursor.fetchall()
                ]
            except Error:
                # Models table might not exist or not be properly linked
                filters['models'] = []
            
            return filters
            
        except Error as e:
            logger.error(f"Error getting available filters: {e}")
            raise

    def fetch_qa_data(self, limit: int = 100, generation_type_id: Optional[int] = None, 
                     generation_params_id: Optional[int] = None, model_id: Optional[int] = None) -> List[Dict]:
        """
        Fetch Q&A data from the database with optional filters.
        
        Args:
            limit (int): Maximum number of records to fetch
            generation_type_id (Optional[int]): Filter by generation type
            generation_params_id (Optional[int]): Filter by generation parameters
            model_id (Optional[int]): Filter by model (if models table exists)
            
        Returns:
            List[Dict]: List of Q&A records with metadata
        """
        try:
            # Build the query with optional filters
            base_query = """
                SELECT 
                    gd.id,
                    gd.data,
                    gd.document_id,
                    d.title,
                    d.doi,
                    d.external_id,
                    d.publication_date,
                    gt.type_name,
                    gp.system_prompt,
                    gp.generation_prompt
                FROM generated_data gd
                JOIN document d ON gd.document_id = d.id
                JOIN generated_type gt ON gd.generation_type = gt.id
                JOIN generation_params gp ON gd.generation_params_id = gp.id
            """
            
            conditions = []
            params = []
            
            if generation_type_id:
                conditions.append("gd.generation_type = %s")
                params.append(generation_type_id)
            
            if generation_params_id:
                conditions.append("gd.generation_params_id = %s")
                params.append(generation_params_id)
            
            if model_id:
                conditions.append("gp.model_id = %s")
                params.append(model_id)
            
            if conditions:
                base_query += " WHERE " + " AND ".join(conditions)
            
            base_query += " ORDER BY gd.id DESC LIMIT %s"
            params.append(limit)
            
            logger.info(f"Executing query to fetch {limit} Q&A records...")
            self.cursor.execute(base_query, params)
            
            results = self.cursor.fetchall()
            
            # Convert to list of dictionaries
            qa_records = []
            for row in results:
                try:
                    qa_data = json.loads(row[1])  # Parse the JSON data
                    
                    record = {
                        'id': row[0],
                        'question': qa_data.get('question', ''),
                        'answer': qa_data.get('answer', ''),
                        'document_id': row[2],
                        'document_title': row[3],
                        'doi': row[4] if row[4] else '',
                        'external_id': row[5] if row[5] else '',
                        'publication_date': row[6].strftime('%Y-%m-%d') if row[6] else '',
                        'generation_type': row[7],
                        'system_prompt': row[8],
                        'generation_prompt': row[9]
                    }
                    qa_records.append(record)
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON data for record {row[0]}: {e}")
                    continue
            
            logger.info(f"Successfully fetched {len(qa_records)} Q&A records")
            return qa_records
            
        except Error as e:
            logger.error(f"Error fetching Q&A data: {e}")
            raise

    def export_to_jsonl(self, qa_records: List[Dict], output_file: str, format_type: str = "phi3") -> str:
        """
        Export Q&A records to JSONL format.
        
        Args:
            qa_records (List[Dict]): Q&A records to export
            output_file (str): Output file path
            format_type (str): Format type - "phi3", "openai", or "simple"
            
        Returns:
            str: Path to the exported file
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for record in qa_records:
                    if format_type == "phi3":
                        # Phi3 MLX format
                        chat_data = {
                            "text": f"<|user|>\n{record['question']} <|end|>\n<|assistant|> \n{record['answer']} <|end|>"
                        }
                    elif format_type == "openai":
                        # OpenAI chat format
                        chat_data = {
                            "messages": [
                                {"role": "user", "content": record['question']},
                                {"role": "assistant", "content": record['answer']}
                            ]
                        }
                    elif format_type == "simple":
                        # Simple prompt/completion format
                        chat_data = {
                            "prompt": record['question'],
                            "completion": record['answer']
                        }
                    else:
                        raise ValueError(f"Unknown format type: {format_type}")
                    
                    # Write JSONL line
                    json_line = json.dumps(chat_data, ensure_ascii=False)
                    f.write(json_line + '\n')
            
            logger.info(f"Exported {len(qa_records)} records to {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"Error exporting to JSONL: {e}")
            raise

def main():
    """Main function to export Q&A data from database."""
    parser = argparse.ArgumentParser(description='Export Q&A pairs from database to JSONL format')
    parser.add_argument('--limit', type=int, default=100, help='Number of records to export (default: 100)')
    parser.add_argument('--output', type=str, help='Output JSONL file path (default: auto-generated)')
    parser.add_argument('--format', type=str, choices=['phi3', 'openai', 'simple'], default='phi3',
                       help='Output format: phi3 for MLX training, openai for OpenAI format, simple for prompt/completion (default: phi3)')
    parser.add_argument('--generation-type-id', type=int, help='Filter by generation type ID')
    parser.add_argument('--generation-params-id', type=int, help='Filter by generation parameters ID')
    parser.add_argument('--model-id', type=int, help='Filter by model ID')
    parser.add_argument('--list-filters', action='store_true', help='List available filter options and exit')
    
    args = parser.parse_args()
    
    exporter = QAExporter()
    
    try:
        # Connect to database
        exporter.connect()
        
        # List available filters if requested
        if args.list_filters:
            filters = exporter.get_available_filters()
            
            print("\n=== Available Filters ===")
            
            print("\nGeneration Types:")
            for gt in filters['generation_types']:
                print(f"  ID {gt['id']}: {gt['name']} - {gt['description']}")
            
            print("\nGeneration Parameters:")
            for gp in filters['generation_params']:
                print(f"  ID {gp['id']}: Used {gp['usage_count']} times")
                print(f"    System: {gp['system_prompt']}")
                print(f"    Prompt: {gp['generation_prompt']}")
            
            if filters['models']:
                print("\nModels:")
                for model in filters['models']:
                    print(f"  ID {model['id']}: {model['name']} - Used {model['usage_count']} times")
            else:
                print("\nModels: No model information available")
            
            return
        
        # Fetch Q&A data
        qa_records = exporter.fetch_qa_data(
            limit=args.limit,
            generation_type_id=args.generation_type_id,
            generation_params_id=args.generation_params_id,
            model_id=args.model_id
        )
        
        if not qa_records:
            logger.warning("No Q&A records found with the specified filters")
            return
        
        # Generate output filename if not provided
        if not args.output:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            args.output = f"exported_qa_{args.format}_{timestamp}.jsonl"
        
        # Export to JSONL
        output_file = exporter.export_to_jsonl(qa_records, args.output, args.format)
        
        logger.info(f"\n✅ Export completed successfully!")
        logger.info(f"📁 Output file: {output_file}")
        logger.info(f"📊 Records exported: {len(qa_records)}")
        logger.info(f"🎯 Format: {args.format}")
        
        # Show sample of exported data
        logger.info(f"\n📝 Sample exported record:")
        with open(output_file, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            sample_data = json.loads(first_line)
            logger.info(f"   {json.dumps(sample_data, indent=2, ensure_ascii=False)[:200]}...")
        
    except Exception as e:
        logger.error(f"Export failed: {e}")
        raise
    finally:
        exporter.disconnect()

if __name__ == "__main__":
    main()
