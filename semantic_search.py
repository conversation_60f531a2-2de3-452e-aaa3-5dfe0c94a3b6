#!/usr/bin/env python3
"""
Semantic Vector Search

This script performs semantic search on document embeddings using vector similarity.
It takes a natural language question, converts it to an embedding using Ollama's
snowflake-arctic-embed2 model, and finds the most similar document chunks.
"""

import os
import sys
import logging
import argparse
from typing import List, Dict, <PERSON><PERSON>
import psycopg2
from psycopg2 import sql, Error
from dotenv import load_dotenv
import ollama
import numpy as np

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class SemanticSearcher:
    """
    Performs semantic search on document embeddings using vector similarity.
    """

    def __init__(self, embedding_model: str = "snowflake-arctic-embed2:latest"):
        """Initialize with database connection and embedding model."""
        self.embedding_model = embedding_model
        self.connection = None
        self.cursor = None
        
        # Database connection setup
        self.db_config = {
            'host': os.getenv('POSTGRES_HOST'),
            'port': os.getenv('POSTGRES_PORT', 5432),
            'database': os.getenv('POSTGRES_DB'),
            'user': os.getenv('POSTGRES_USER'),
            'password': os.getenv('POSTGRES_PASSWORD')
        }
        
        # Validate required environment variables
        required_vars = ['POSTGRES_HOST', 'POSTGRES_DB', 'POSTGRES_USER', 'POSTGRES_PASSWORD']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

    def connect(self):
        """Establish database connection."""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info(f"Connected to database: {self.db_config['database']}")
            
            # Verify that pgvector extension is available
            self.cursor.execute("SELECT 1 FROM pg_extension WHERE extname = 'vector';")
            if not self.cursor.fetchone():
                logger.warning("pgvector extension not found - vector operations may not work")
            
        except Error as e:
            logger.error(f"Database connection error: {e}")
            raise

    def disconnect(self):
        """Close database connection."""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("Database connection closed")

    def get_question_embedding(self, question: str) -> List[float]:
        """
        Generate embedding for the input question using Ollama.
        
        Args:
            question (str): The question to embed
            
        Returns:
            List[float]: The embedding vector
        """
        try:
            logger.info(f"Generating embedding for question using model: {self.embedding_model}")
            
            # Generate embedding using Ollama
            response = ollama.embeddings(
                model=self.embedding_model,
                prompt=question
            )
            
            embedding = response['embedding']
            logger.info(f"Generated embedding with {len(embedding)} dimensions")
            
            return embedding
            
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            raise

    def search_similar_chunks(self, question_embedding: List[float], limit: int = 20) -> List[Dict]:
        """
        Search for similar document chunks using vector similarity.
        
        Args:
            question_embedding (List[float]): The question embedding vector
            limit (int): Number of results to return
            
        Returns:
            List[Dict]: List of similar chunks with metadata
        """
        try:
            # Convert embedding to string format for PostgreSQL
            embedding_str = '[' + ','.join(map(str, question_embedding)) + ']'
            
            # Optimized query to find most similar embeddings using cosine similarity
            # Using <=> operator for cosine distance (lower is more similar)
            # Optimization: Calculate distance only once and use subquery to limit joins
            query = """
                WITH similar_embeddings AS (
                    SELECT
                        chunk_id,
                        embedding <=> %s::vector as distance
                    FROM emb_1024
                    ORDER BY embedding <=> %s::vector
                    LIMIT %s
                )
                SELECT
                    c.document_id,
                    d.title as document_title,
                    c.text as chunk_text,
                    se.distance,
                    1 - se.distance as similarity_score
                FROM similar_embeddings se
                JOIN chunks c ON se.chunk_id = c.id
                JOIN document d ON c.document_id = d.id
                ORDER BY se.distance;
            """

            logger.info(f"Searching for {limit} most similar chunks...")
            self.cursor.execute(query, (embedding_str, embedding_str, limit))
            
            results = self.cursor.fetchall()
            
            # Convert to list of dictionaries
            similar_chunks = []
            for row in results:
                chunk = {
                    'document_id': row[0],
                    'document_title': row[1],
                    'chunk_text': row[3],
                    'distance': float(row[4]),
                    'similarity_score': float(row[5])
                }
                similar_chunks.append(chunk)
            
            logger.info(f"Found {len(similar_chunks)} similar chunks")
            return similar_chunks
            
        except Error as e:
            logger.error(f"Error searching similar chunks: {e}")
            raise

    def display_results(self, results: List[Dict], show_text: bool = False):
        """
        Display search results in a formatted way.
        
        Args:
            results (List[Dict]): Search results
            show_text (bool): Whether to show chunk text content
        """
        if not results:
            logger.info("No results found")
            return
        
        print(f"\n{'='*80}")
        print(f"SEMANTIC SEARCH RESULTS ({len(results)} chunks)")
        print(f"{'='*80}")
        
        for i, result in enumerate(results, 1):
            print(f"\n{i:2d}. Document ID: {result['document_id']} | Similarity: {result['similarity_score']:.4f}")
            print(f"    Document Title: {result['document_title']}")
            
            if result['chunk_title'] and result['chunk_title'].strip():
                print(f"    Chunk Title: {result['chunk_title']}")
            
            if show_text:
                # Show first 200 characters of chunk text
                text_preview = result['chunk_text'][:200] + "..." if len(result['chunk_text']) > 200 else result['chunk_text']
                print(f"    Text Preview: {text_preview}")
            
            print(f"    Distance: {result['distance']:.6f}")

    def get_database_stats(self) -> Dict:
        """Get statistics about the embedding database."""
        try:
            stats = {}
            
            # Count total documents
            self.cursor.execute("SELECT COUNT(*) FROM document;")
            stats['total_documents'] = self.cursor.fetchone()[0]
            
            # Count total chunks
            self.cursor.execute("SELECT COUNT(*) FROM chunk;")
            stats['total_chunks'] = self.cursor.fetchone()[0]
            
            # Count total embeddings
            self.cursor.execute("SELECT COUNT(*) FROM emb_1024;")
            stats['total_embeddings'] = self.cursor.fetchone()[0]
            
            # Check embedding dimensions
            self.cursor.execute("SELECT array_length(embedding, 1) FROM emb_1024 LIMIT 1;")
            result = self.cursor.fetchone()
            stats['embedding_dimensions'] = result[0] if result else 0
            
            return stats
            
        except Error as e:
            logger.error(f"Error getting database stats: {e}")
            return {}

def main():
    """Main function to perform semantic search."""
    parser = argparse.ArgumentParser(description='Perform semantic search on document embeddings')
    parser.add_argument('question', type=str, help='The question to search for')
    parser.add_argument('--limit', type=int, default=20, help='Number of results to return (default: 20)')
    parser.add_argument('--show-text', action='store_true', help='Show chunk text content in results')
    parser.add_argument('--stats', action='store_true', help='Show database statistics')
    parser.add_argument('--model', type=str, default='snowflake-arctic-embed2:latest', 
                       help='Ollama embedding model to use (default: snowflake-arctic-embed2:latest)')
    
    args = parser.parse_args()
    
    searcher = SemanticSearcher(embedding_model=args.model)
    
    try:
        # Connect to database
        searcher.connect()
        
        # Show database statistics if requested
        if args.stats:
            stats = searcher.get_database_stats()
            print(f"\n{'='*50}")
            print("DATABASE STATISTICS")
            print(f"{'='*50}")
            print(f"Total documents: {stats.get('total_documents', 0):,}")
            print(f"Total chunks: {stats.get('total_chunks', 0):,}")
            print(f"Total embeddings: {stats.get('total_embeddings', 0):,}")
            print(f"Embedding dimensions: {stats.get('embedding_dimensions', 0)}")
            print()
        
        # Generate embedding for the question
        logger.info(f"Processing question: {args.question}")
        question_embedding = searcher.get_question_embedding(args.question)
        
        # Search for similar chunks
        results = searcher.search_similar_chunks(question_embedding, args.limit)
        
        # Display results
        searcher.display_results(results, args.show_text)
        
        # Summary
        if results:
            best_score = results[0]['similarity_score']
            worst_score = results[-1]['similarity_score']
            print(f"\nSimilarity range: {worst_score:.4f} - {best_score:.4f}")
            print(f"Unique documents: {len(set(r['document_id'] for r in results))}")
        
    except Exception as e:
        logger.error(f"Semantic search failed: {e}")
        sys.exit(1)
    finally:
        searcher.disconnect()

if __name__ == "__main__":
    main()
