#!/usr/bin/env python3
"""
Test script for database storage functionality in abstract_qa.py
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the current directory to the path so we can import abstract_qa
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from abstract_qa import AbstractQARetriever

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_database_storage():
    """Test the database storage functionality."""
    
    # Initialize the retriever
    retriever = AbstractQARetriever(model_name="mistral-small3.2")
    
    try:
        # Connect to database
        retriever.connect()
        logger.info("Database connection successful")
        
        # Get a small sample of documents (just 2 for testing)
        documents = retriever.get_2025_abstracts(limit=2)
        
        if not documents:
            logger.error("No documents retrieved for testing")
            return False
        
        logger.info(f"Retrieved {len(documents)} documents for testing")
        
        # Test database storage
        logger.info("Testing database storage...")
        stats = retriever.generate_qa_to_database(documents)
        
        logger.info("Database storage test results:")
        logger.info(f"  Total documents: {stats['total_documents']}")
        logger.info(f"  Processed documents: {stats['processed_documents']}")
        logger.info(f"  Failed documents: {stats['failed_documents']}")
        logger.info(f"  Skipped documents: {stats['skipped_documents']}")
        logger.info(f"  Total Q&A pairs: {stats['total_qa_pairs']}")
        logger.info(f"  Generation params ID: {stats['generation_params_id']}")
        
        # Verify data was stored
        if stats['total_qa_pairs'] > 0:
            logger.info("✅ Database storage test PASSED")
            return True
        else:
            logger.error("❌ Database storage test FAILED - no Q&A pairs were stored")
            return False
            
    except Exception as e:
        logger.error(f"❌ Database storage test FAILED with error: {e}")
        return False
    finally:
        retriever.disconnect()

def test_database_tables():
    """Test that the required database tables exist."""
    
    retriever = AbstractQARetriever()
    
    try:
        retriever.connect()
        
        # Check if required tables exist
        tables_to_check = ['generation_params', 'generated_type', 'generated_data', 'document']
        
        for table in tables_to_check:
            retriever.cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """, (table,))
            
            exists = retriever.cursor.fetchone()[0]
            if exists:
                logger.info(f"✅ Table '{table}' exists")
            else:
                logger.error(f"❌ Table '{table}' does not exist")
                return False
        
        # Check if AQA type exists in generated_type
        retriever.cursor.execute("SELECT COUNT(*) FROM generated_type WHERE type_name = 'AQA'")
        aqa_count = retriever.cursor.fetchone()[0]
        
        if aqa_count > 0:
            logger.info("✅ AQA generation type exists")
        else:
            logger.error("❌ AQA generation type does not exist")
            return False
        
        logger.info("✅ Database schema test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database schema test FAILED with error: {e}")
        return False
    finally:
        retriever.disconnect()

if __name__ == "__main__":
    logger.info("Starting database storage tests...")
    
    # Test 1: Check database schema
    logger.info("\n=== Test 1: Database Schema ===")
    schema_ok = test_database_tables()
    
    if schema_ok:
        # Test 2: Test database storage functionality
        logger.info("\n=== Test 2: Database Storage ===")
        storage_ok = test_database_storage()
        
        if storage_ok:
            logger.info("\n🎉 All tests PASSED! Database storage is working correctly.")
        else:
            logger.error("\n💥 Database storage test FAILED!")
            sys.exit(1)
    else:
        logger.error("\n💥 Database schema test FAILED!")
        sys.exit(1)
