#!/usr/bin/env python3
"""
Large-Scale Vector Search - Optimized for 37M+ Embeddings

This script is specifically optimized for very large embedding databases
with tens of millions of vectors.
"""

import os
import sys
import logging
import argparse
import time
from typing import List, Dict, Optional
import psycopg2
from psycopg2 import Error
from dotenv import load_dotenv
import ollama

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class LargeScaleVectorSearch:
    """Optimized vector search for massive databases."""

    def __init__(self, embedding_model: str = "snowflake-arctic-embed2:latest"):
        self.embedding_model = embedding_model
        self.connection = None
        self.cursor = None
        
        # Database connection
        self.db_config = {
            'host': os.getenv('POSTGRES_HOST'),
            'port': os.getenv('POSTGRES_PORT', 5432),
            'database': os.getenv('POSTGRES_DB'),
            'user': os.getenv('POSTGRES_USER'),
            'password': os.getenv('POSTGRES_PASSWORD')
        }

    def connect(self):
        """Connect with optimizations for large databases."""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            
            # Optimize for large-scale operations
            self.cursor.execute("SET work_mem = '512MB';")  # Increased for large sorts
            self.cursor.execute("SET random_page_cost = 1.1;")  # SSD optimization
            self.cursor.execute("SET effective_cache_size = '8GB';")  # Adjust based on your RAM
            self.cursor.execute("SET maintenance_work_mem = '1GB';")
            
            # For HNSW specifically
            self.cursor.execute("SET hnsw.ef_search = 100;")  # Increase search quality
            
            logger.info("Connected with large-scale optimizations")
        except Error as e:
            logger.error(f"Connection error: {e}")
            raise

    def disconnect(self):
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()

    def get_embedding(self, text: str) -> List[float]:
        """Get embedding from Ollama."""
        try:
            response = ollama.embeddings(model=self.embedding_model, prompt=text)
            return response['embedding']
        except Exception as e:
            logger.error(f"Embedding error: {e}")
            raise

    def search_ultra_fast(self, question: str, limit: int = 20, model_id: Optional[int] = None) -> List[Dict]:
        """Ultra-fast search optimized for 37M+ vectors."""
        
        start_time = time.time()
        
        # Get embedding
        embedding = self.get_embedding(question)
        embedding_str = '[' + ','.join(map(str, embedding)) + ']'
        embedding_time = time.time() - start_time
        
        # Optimized vector search with optional model filtering
        vector_start = time.time()
        
        if model_id:
            # Filter by model_id if specified (much faster for multi-model setups)
            vector_query = """
                SELECT 
                    chunk_id,
                    embedding <=> %s::vector as distance
                FROM emb_1024
                WHERE model_id = %s
                ORDER BY embedding <=> %s::vector
                LIMIT %s;
            """
            params = (embedding_str, model_id, embedding_str, limit)
        else:
            # Search all models
            vector_query = """
                SELECT 
                    chunk_id,
                    embedding <=> %s::vector as distance
                FROM emb_1024
                ORDER BY embedding <=> %s::vector
                LIMIT %s;
            """
            params = (embedding_str, embedding_str, limit)
        
        logger.info(f"Searching {limit} vectors from 37M+ embeddings...")
        self.cursor.execute(vector_query, params)
        vector_results = self.cursor.fetchall()
        vector_time = time.time() - vector_start
        
        if not vector_results:
            logger.warning("No results found")
            return []
        
        # Get metadata efficiently
        metadata_start = time.time()
        chunk_ids = [row[0] for row in vector_results]
        distance_map = {row[0]: row[1] for row in vector_results}
        
        # Use prepared statement for better performance with large IN clauses
        chunk_ids_str = ','.join(map(str, chunk_ids))
        
        metadata_query = f"""
            SELECT 
                c.id as chunk_id,
                c.document_id,
                d.title as document_title,
                LEFT(c.text, 500) as chunk_text_preview
            FROM chunks c
            JOIN document d ON c.document_id = d.id
            WHERE c.id IN ({chunk_ids_str})
        """
        
        self.cursor.execute(metadata_query)
        metadata_results = self.cursor.fetchall()
        metadata_time = time.time() - metadata_start
        
        # Combine results maintaining order
        final_results = []
        metadata_dict = {row[0]: row for row in metadata_results}
        
        for chunk_id in chunk_ids:
            if chunk_id in metadata_dict:
                metadata = metadata_dict[chunk_id]
                distance = distance_map[chunk_id]
                
                final_results.append({
                    'chunk_id': chunk_id,
                    'document_id': metadata[1],
                    'document_title': metadata[2],
                    'chunk_text_preview': metadata[3],
                    'distance': float(distance),
                    'similarity_score': float(1 - distance)
                })
        
        total_time = time.time() - start_time
        
        logger.info(f"Search completed in {total_time:.3f}s")
        logger.info(f"  Embedding: {embedding_time:.3f}s")
        logger.info(f"  Vector search: {vector_time:.3f}s") 
        logger.info(f"  Metadata: {metadata_time:.3f}s")
        
        return final_results

    def search_with_ef_search_tuning(self, question: str, limit: int = 20, ef_search: int = 100) -> List[Dict]:
        """Search with tunable HNSW ef_search parameter."""
        
        # Tune HNSW search parameter
        self.cursor.execute(f"SET hnsw.ef_search = {ef_search};")
        logger.info(f"Set HNSW ef_search to {ef_search}")
        
        return self.search_ultra_fast(question, limit)

    def get_model_stats(self) -> Dict:
        """Get statistics by model_id to help with filtering."""
        try:
            model_query = """
                SELECT 
                    model_id,
                    COUNT(*) as embedding_count,
                    MIN(id) as min_id,
                    MAX(id) as max_id
                FROM emb_1024
                GROUP BY model_id
                ORDER BY embedding_count DESC;
            """
            
            self.cursor.execute(model_query)
            results = self.cursor.fetchall()
            
            model_stats = {}
            for row in results:
                model_id, count, min_id, max_id = row
                model_stats[model_id] = {
                    'count': count,
                    'min_id': min_id,
                    'max_id': max_id
                }
            
            logger.info("Model statistics:")
            for model_id, stats in model_stats.items():
                logger.info(f"  Model {model_id}: {stats['count']:,} embeddings")
            
            return model_stats
            
        except Error as e:
            logger.error(f"Error getting model stats: {e}")
            return {}

    def benchmark_search_methods(self, question: str, limit: int = 10):
        """Benchmark different search approaches."""
        
        logger.info(f"\n{'='*60}")
        logger.info("BENCHMARKING SEARCH METHODS")
        logger.info(f"{'='*60}")
        logger.info(f"Question: {question}")
        logger.info(f"Limit: {limit}")
        
        # Get model stats first
        model_stats = self.get_model_stats()
        
        # Test 1: Default search
        logger.info(f"\n1. Default search (ef_search=100):")
        start = time.time()
        results1 = self.search_ultra_fast(question, limit)
        time1 = time.time() - start
        logger.info(f"   Results: {len(results1)}, Time: {time1:.3f}s")
        
        # Test 2: Lower ef_search for speed
        logger.info(f"\n2. Fast search (ef_search=40):")
        start = time.time()
        results2 = self.search_with_ef_search_tuning(question, limit, ef_search=40)
        time2 = time.time() - start
        logger.info(f"   Results: {len(results2)}, Time: {time2:.3f}s")
        
        # Test 3: Higher ef_search for quality
        logger.info(f"\n3. High-quality search (ef_search=200):")
        start = time.time()
        results3 = self.search_with_ef_search_tuning(question, limit, ef_search=200)
        time3 = time.time() - start
        logger.info(f"   Results: {len(results3)}, Time: {time3:.3f}s")
        
        # Test 4: Model-filtered search (if multiple models)
        if len(model_stats) > 1:
            # Use the model with most embeddings
            best_model_id = max(model_stats.keys(), key=lambda k: model_stats[k]['count'])
            logger.info(f"\n4. Model-filtered search (model_id={best_model_id}):")
            start = time.time()
            results4 = self.search_ultra_fast(question, limit, model_id=best_model_id)
            time4 = time.time() - start
            logger.info(f"   Results: {len(results4)}, Time: {time4:.3f}s")
        
        # Compare quality
        if results1 and results2:
            score_diff = abs(results1[0]['similarity_score'] - results2[0]['similarity_score'])
            logger.info(f"\nQuality comparison (top result similarity difference): {score_diff:.4f}")

def main():
    parser = argparse.ArgumentParser(description='Large-scale vector search (37M+ embeddings)')
    parser.add_argument('question', type=str, help='Search question')
    parser.add_argument('--limit', type=int, default=20, help='Number of results')
    parser.add_argument('--model-id', type=int, help='Filter by specific model_id')
    parser.add_argument('--ef-search', type=int, default=100, help='HNSW ef_search parameter (40-400)')
    parser.add_argument('--benchmark', action='store_true', help='Benchmark different search methods')
    parser.add_argument('--model-stats', action='store_true', help='Show model statistics')
    parser.add_argument('--show-text', action='store_true', help='Show chunk text preview')
    
    args = parser.parse_args()
    
    searcher = LargeScaleVectorSearch()
    
    try:
        searcher.connect()
        
        if args.model_stats:
            searcher.get_model_stats()
            return
        
        if args.benchmark:
            searcher.benchmark_search_methods(args.question, args.limit)
            return
        
        # Regular search
        if args.model_id:
            logger.info(f"Searching with model_id filter: {args.model_id}")
            results = searcher.search_ultra_fast(args.question, args.limit, args.model_id)
        else:
            results = searcher.search_with_ef_search_tuning(args.question, args.limit, args.ef_search)
        
        # Display results
        print(f"\n{'='*60}")
        print(f"SEARCH RESULTS ({len(results)} from 37M+ embeddings)")
        print(f"{'='*60}")
        
        for i, result in enumerate(results, 1):
            print(f"\n{i:2d}. Document ID: {result['document_id']} | "
                  f"Similarity: {result['similarity_score']:.4f}")
            print(f"    Title: {result['document_title'][:80]}...")
            
            if args.show_text and result['chunk_text_preview']:
                print(f"    Text: {result['chunk_text_preview'][:150]}...")
        
        if results:
            best_score = max(r['similarity_score'] for r in results)
            worst_score = min(r['similarity_score'] for r in results)
            unique_docs = len(set(r['document_id'] for r in results))
            print(f"\nSimilarity range: {worst_score:.4f} - {best_score:.4f}")
            print(f"Unique documents: {unique_docs}")
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        sys.exit(1)
    finally:
        searcher.disconnect()

if __name__ == "__main__":
    main()
