#!/usr/bin/env python3
"""
Test script for tsquery training data generation.
"""

import os
import sys
import json
import logging
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from generate_tsquery_training_data import TsqueryTrainingGenerator

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_term_extraction():
    """Test the key term extraction functionality."""
    
    generator = TsqueryTrainingGenerator()
    
    test_texts = [
        "What is the cutoff value in ONSD measured by ultrasound for raised ICP?",
        "How sensitive is troponin for diagnosing <PERSON> in the emergency department?",
        "What is the specificity of CT scan for detecting intracranial hemorrhage?",
        "Can MRI accurately diagnose acute stroke within 6 hours of onset?"
    ]
    
    logger.info("=== Testing Term Extraction ===")
    
    for text in test_texts:
        terms = generator.extract_key_terms(text)
        logger.info(f"Text: {text}")
        logger.info(f"Extracted terms: {sorted(terms)}")
        logger.info("")

def test_basic_tsquery_generation():
    """Test the basic tsquery generation without AI."""
    
    generator = TsqueryTrainingGenerator()
    
    test_cases = [
        {
            "question": "What is the cutoff value in ONSD measured by ultrasound for raised ICP?",
            "answer": "The cutoff value for ONSD is typically 5.0-5.7 mm for detecting raised intracranial pressure.",
            "expected_terms": ["ONSD", "ultrasound", "ICP", "cutoff"]
        },
        {
            "question": "What is the sensitivity of troponin for MI diagnosis?",
            "answer": "Troponin has 95-99% sensitivity for myocardial infarction diagnosis.",
            "expected_terms": ["troponin", "MI", "sensitivity"]
        },
        {
            "question": "How effective is CPR for achieving ROSC?",
            "answer": "CPR achieves ROSC in 25-30% of cardiac arrest cases.",
            "expected_terms": ["CPR", "ROSC", "effectiveness"]
        }
    ]
    
    logger.info("=== Testing Basic tsquery Generation ===")
    
    for i, case in enumerate(test_cases, 1):
        logger.info(f"\nTest case {i}:")
        logger.info(f"Question: {case['question']}")
        logger.info(f"Answer: {case['answer']}")
        
        tsquery = generator.generate_basic_tsquery(case['question'], case['answer'])
        logger.info(f"Generated tsquery: {tsquery}")
        
        # Check if expected terms are present
        for term in case['expected_terms']:
            if term.lower() in tsquery.lower():
                logger.info(f"✅ Found expected term: {term}")
            else:
                logger.warning(f"⚠️ Missing expected term: {term}")

def test_medical_synonyms():
    """Test that medical synonyms are properly expanded."""
    
    generator = TsqueryTrainingGenerator()
    
    # Test questions with medical abbreviations
    test_cases = [
        {
            "question": "What is the MI mortality rate?",
            "answer": "Myocardial infarction mortality is approximately 10%.",
            "should_contain": ["MI", "myocardial infarction"]
        },
        {
            "question": "How is ICP measured?",
            "answer": "Intracranial pressure can be measured invasively or non-invasively.",
            "should_contain": ["ICP", "intracranial pressure"]
        },
        {
            "question": "What ECG changes indicate ischemia?",
            "answer": "Electrocardiogram changes include ST depression and T-wave inversion.",
            "should_contain": ["ECG", "electrocardiogram"]
        }
    ]
    
    logger.info("=== Testing Medical Synonym Expansion ===")
    
    for i, case in enumerate(test_cases, 1):
        logger.info(f"\nTest case {i}:")
        logger.info(f"Question: {case['question']}")
        
        tsquery = generator.generate_basic_tsquery(case['question'], case['answer'])
        logger.info(f"Generated tsquery: {tsquery}")
        
        # Check for synonym expansion
        for term in case['should_contain']:
            if term.lower() in tsquery.lower():
                logger.info(f"✅ Contains: {term}")
            else:
                logger.warning(f"⚠️ Missing: {term}")

def test_tsquery_syntax():
    """Test that generated tsqueries have valid syntax."""
    
    generator = TsqueryTrainingGenerator()
    
    test_cases = [
        {
            "question": "What is the diagnostic accuracy of ultrasound for appendicitis?",
            "answer": "Ultrasound has 85-95% accuracy for diagnosing acute appendicitis."
        },
        {
            "question": "How effective is thrombolysis for stroke treatment?",
            "answer": "Thrombolysis improves outcomes when given within 4.5 hours of stroke onset."
        }
    ]
    
    logger.info("=== Testing tsquery Syntax Validation ===")
    
    for i, case in enumerate(test_cases, 1):
        logger.info(f"\nTest case {i}:")
        
        tsquery = generator.generate_basic_tsquery(case['question'], case['answer'])
        logger.info(f"Generated tsquery: {tsquery}")
        
        # Basic syntax checks
        syntax_checks = [
            ("Balanced parentheses", check_balanced_parentheses(tsquery)),
            ("Proper quotes", check_proper_quotes(tsquery)),
            ("Valid operators", check_valid_operators(tsquery)),
            ("No empty groups", check_no_empty_groups(tsquery))
        ]
        
        for check_name, is_valid in syntax_checks:
            if is_valid:
                logger.info(f"✅ {check_name}: PASS")
            else:
                logger.error(f"❌ {check_name}: FAIL")

def check_balanced_parentheses(text: str) -> bool:
    """Check if parentheses are balanced."""
    count = 0
    for char in text:
        if char == '(':
            count += 1
        elif char == ')':
            count -= 1
        if count < 0:
            return False
    return count == 0

def check_proper_quotes(text: str) -> bool:
    """Check if quotes are properly paired."""
    quote_count = text.count('"')
    return quote_count % 2 == 0

def check_valid_operators(text: str) -> bool:
    """Check if only valid operators are used."""
    import re
    # Remove quoted content and check remaining operators
    no_quotes = re.sub(r'"[^"]*"', '', text)
    invalid_chars = set(no_quotes) - set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 &|()_-')
    return len(invalid_chars) == 0

def check_no_empty_groups(text: str) -> bool:
    """Check for empty parentheses groups."""
    import re
    empty_groups = re.search(r'\(\s*\)', text)
    return empty_groups is None

def test_training_data_format():
    """Test that training data is generated in correct format."""
    
    generator = TsqueryTrainingGenerator()
    
    # Mock Q&A data
    qa_pairs = [
        {
            "question": "What is the normal range for troponin levels?",
            "answer": "Normal troponin levels are typically less than 0.04 ng/mL.",
            "title": "Cardiac biomarkers in clinical practice",
            "abstract": "This study examines troponin levels in healthy individuals."
        }
    ]
    
    logger.info("=== Testing Training Data Format ===")
    
    formats = ['phi3', 'openai', 'simple']
    
    for format_type in formats:
        logger.info(f"\nTesting {format_type} format:")
        
        training_data = generator.generate_training_data(qa_pairs, format_type)
        
        if training_data:
            example = training_data[0]
            logger.info(f"Generated example: {json.dumps(example, indent=2)[:300]}...")
            
            # Format-specific validation
            if format_type == 'phi3':
                assert 'text' in example
                assert '<|user|>' in example['text']
                assert '<|assistant|>' in example['text']
                logger.info("✅ phi3 format validation passed")
            
            elif format_type == 'openai':
                assert 'messages' in example
                assert len(example['messages']) == 2
                assert example['messages'][0]['role'] == 'user'
                assert example['messages'][1]['role'] == 'assistant'
                logger.info("✅ openai format validation passed")
            
            elif format_type == 'simple':
                assert 'prompt' in example
                assert 'completion' in example
                logger.info("✅ simple format validation passed")
        else:
            logger.error(f"❌ No training data generated for {format_type} format")

if __name__ == "__main__":
    logger.info("Starting tsquery training data tests...")
    
    # Run all tests
    test_term_extraction()
    test_basic_tsquery_generation()
    test_medical_synonyms()
    test_tsquery_syntax()
    test_training_data_format()
    
    logger.info("\n🎉 All tests completed!")
    logger.info("\nTo generate actual training data:")
    logger.info("  python generate_tsquery_training_data.py --limit 50")
    logger.info("  python generate_tsquery_training_data.py --test  # For AI testing")
