# Database Storage Feature for abstract_qa.py

## Overview

Added direct database storage functionality to `abstract_qa.py` that allows generated Q&A pairs to be stored directly in the PostgreSQL database instead of generating text files. This provides better traceability, data management, and integration with machine learning workflows.

## New Database Tables

The following tables were added to support this functionality:

### generation_params
```sql
CREATE TABLE generation_params (
    id serial primary key,
    model_id integer references models(id),
    system_prompt text,
    generation_prompt text
);
```
Stores the AI model and prompts used for generation, enabling reproducibility.

### generated_type
```sql
CREATE TABLE generated_type (
    id serial primary key,
    type_name text,
    description TEXT
);
```
Categorizes different types of generated data (e.g., Q&A pairs, summaries).

### generated_data
```sql
CREATE TABLE generated_data (
    id serial primary key,
    document_id integer references document(id),
    generation_params_id integer references generation_params(id),
    generation_type integer references generated_type(id),
    data TEXT
);
```
Stores the actual generated data in JSON format with full traceability.

## Required Setup

1. **Create the new tables** in your PostgreSQL database
2. **Insert the AQA type**:
   ```sql
   INSERT INTO generated_type (type_name, description)
   VALUES ('AQA', 'Question and Answer pairs generated from title + abstracts');
   ```

## New Functionality

### Command Line Options
```bash
# Store Q&A pairs directly in database
python abstract_qa.py --limit 50 --to-database

# With date range
python abstract_qa.py --from-date 2025-01-01 --to-date 2025-01-31 --to-database

# Process only unprocessed documents (NEW)
python abstract_qa.py --unprocessed-only --limit 100 --to-database

# Check processing statistics
python abstract_qa.py --unprocessed-only --stats-only
```

### New Methods Added

1. **`get_document_id_by_source(doi, external_id)`**
   - Retrieves document ID from database by DOI or PMID

2. **`get_or_create_generation_params(system_prompt, generation_prompt)`**
   - Creates or retrieves generation parameters record

3. **`save_qa_to_database(document_id, generation_params_id, question, answer)`**
   - Saves individual Q&A pairs to the database

4. **`generate_qa_to_database(documents, system_prompt, generation_prompt)`**
   - Main method that orchestrates the database storage process

5. **`get_unprocessed_documents(limit)` (NEW)**
   - Retrieves newest documents (by ID) that haven't been processed for AQA yet
   - Orders by document_id DESC to get newest documents first
   - Excludes documents that already have AQA entries

6. **`get_processing_stats()` (NEW)**
   - Returns statistics about document processing status
   - Shows total, processed, unprocessed counts and averages

### Enhanced extract_qa_pairs Method
- Added "database" format option that returns separate question/answer pairs
- Maintains compatibility with existing formats (phi3, qas, prompt_completion)

## Data Format

Q&A pairs are stored in JSON format in the `data` column:
```json
{
  "question": "What is the effect of aspirin on cardiovascular mortality?",
  "answer": "Aspirin reduces cardiovascular mortality in high-risk patients by 15-20% in primary prevention studies."
}
```

## Benefits

1. **Full Traceability**: Each Q&A pair is linked to its source document and generation parameters
2. **Reproducibility**: Generation parameters are stored for future reference
3. **Data Integrity**: Database constraints ensure data consistency
4. **ML Integration**: Data is immediately available for training without file processing
5. **Scalability**: Database storage handles large datasets more efficiently than files
6. **Incremental Processing**: Unprocessed documents mode enables efficient batch processing
7. **Duplicate Prevention**: Automatic detection prevents reprocessing of documents
8. **Progress Tracking**: Processing statistics show completion status

## Testing

### Test Scripts
- `test_database_storage.py`: Validates database functionality
- `test_unprocessed_documents.py`: Tests unprocessed documents functionality
- `examples/database_storage_example.py`: Demonstrates usage

### Manual Testing
```bash
# Test with small dataset
python abstract_qa.py --limit 2 --to-database

# Verify data was stored
python test_database_storage.py
```

## Usage Examples

### Basic Usage
```bash
python abstract_qa.py --limit 100 --to-database
```

### With Custom Date Range
```bash
python abstract_qa.py --from-date 2025-01-01 --to-date 2025-01-31 --to-database
```

### Process Only Unprocessed Documents (NEW)
```bash
# Process newest unprocessed documents
python abstract_qa.py --unprocessed-only --limit 50 --to-database

# Check what needs to be processed
python abstract_qa.py --unprocessed-only --stats-only

# Process all remaining unprocessed documents in batches
python abstract_qa.py --unprocessed-only --limit 1000 --to-database
```

### Programmatic Usage
```python
from abstract_qa import AbstractQARetriever

retriever = AbstractQARetriever()
retriever.connect()

documents = retriever.get_2025_abstracts(limit=10)
stats = retriever.generate_qa_to_database(documents)

print(f"Generated {stats['total_qa_pairs']} Q&A pairs")
```

## Database Queries

### View Generated Q&A Pairs
```sql
SELECT 
    gd.id,
    d.title,
    d.doi,
    gd.data::json->>'question' as question,
    gd.data::json->>'answer' as answer
FROM generated_data gd
JOIN document d ON gd.document_id = d.id
JOIN generated_type gt ON gd.generation_type = gt.id
WHERE gt.type_name = 'AQA'
ORDER BY gd.id DESC;
```

### View Generation Statistics
```sql
SELECT 
    gp.id,
    COUNT(gd.id) as qa_pairs_count,
    gp.system_prompt,
    LEFT(gp.generation_prompt, 100) as generation_prompt_preview
FROM generation_params gp
LEFT JOIN generated_data gd ON gp.id = gd.generation_params_id
GROUP BY gp.id, gp.system_prompt, gp.generation_prompt
ORDER BY gp.id DESC;
```

## Migration Notes

- Existing file-based workflows continue to work unchanged
- The `--to-database` flag is optional and doesn't affect existing functionality
- Database storage is an additional option, not a replacement

## Future Enhancements

1. **Model Tracking**: Link to `models` table when available
2. **Batch Processing**: Optimize for large-scale generation
3. **Quality Metrics**: Store quality scores with Q&A pairs
4. **Export Functions**: Export database data to various formats
5. **Web Interface**: Admin interface for managing generated data
