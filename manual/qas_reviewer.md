# Q/A/S Human Reviewer

## Overview

The Q/A/S Human Reviewer is a graphical user interface application built with the flet library that allows human reviewers to edit and validate Q/A/S (Question/Answer/Source) formatted text files. The application provides an intuitive interface for reviewing machine-generated question-answer pairs and their associated sources.

## Features

- **File Selection**: Cross-platform file input with browse button and manual path entry (macOS compatible)
- **Editable Fields**: Text fields for editing questions and answers
- **Source Display**: Read-only display of source information (DOI or PMID)
- **Abstract Viewer**: Markdown display of publication abstracts fetched from PostgreSQL database
- **Navigation**: Previous/Next buttons to navigate between entries
- **Auto-Save**: Automatic saving of changes to new files with 'he_' prefix
- **Error Handling**: Comprehensive error handling and user feedback

## File Format

The application expects text files in the following Q/A/S format:

```
Q: What is the effect of aspirin on cardiovascular mortality?
A: Aspirin reduces cardiovascular mortality in high-risk patients.
S: 10.1234/aspirin-study

Q: How does MRI compare to CT for stroke detection?
A: MRI is more sensitive than CT for detecting early stroke signs.
S: PMID:12345678
```

### Format Rules

- Each entry consists of exactly 3 lines: Q:, A:, and S:
- Entries are separated by blank lines
- Questions start with "Q: "
- Answers start with "A: "
- Sources start with "S: " and can be either DOI or PMID:external_id format
- All three components (Q, A, S) are required for a valid entry

## Installation and Setup

### Prerequisites

1. **Python 3.12+** with the following packages:
   - flet>=0.28.3
   - psycopg2>=2.9.10
   - python-dotenv
   - ollama>=0.5.1

2. **PostgreSQL Database** with the following tables:
   - `document` table containing:
     - `id` (serial primary key)
     - `title` (text)
     - `abstract` (text)
     - `publication_date` (date)
     - `doi` (text)
     - `external_id` (text)

   - `models` table (optional, for tracking AI models used)

   - `generation_params` table:
     - `id` (serial primary key)
     - `model_id` (integer, references models.id, nullable)
     - `system_prompt` (text)
     - `generation_prompt` (text)

   - `generated_type` table:
     - `id` (serial primary key)
     - `type_name` (text)
     - `description` (text)

   - `generated_data` table:
     - `id` (serial primary key)
     - `document_id` (integer, references document.id)
     - `generation_params_id` (integer, references generation_params.id)
     - `generation_type` (integer, references generated_type.id)
     - `data` (text, JSON format)

3. **Environment Variables**:
   ```bash
   POSTGRES_HOST=your_host
   POSTGRES_PORT=5432
   POSTGRES_DB=your_database
   POSTGRES_USER=your_username
   POSTGRES_PASSWORD=your_password
   ```

4. **Required Database Data**:
   ```sql
   INSERT INTO generated_type (type_name, description)
   VALUES ('AQA', 'Question and Answer pairs generated from title + abstracts');
   ```

## Database Storage Mode (New Feature)

The `abstract_qa.py` script now supports direct database storage of generated Q&A pairs:

```bash
# Generate Q&A pairs and store directly in database
python abstract_qa.py --limit 50 --to-database

# Generate Q&A pairs for specific date range and store in database
python abstract_qa.py --from-date 2025-01-01 --to-date 2025-01-31 --to-database

# Process only unprocessed documents (newest first by document_id)
python abstract_qa.py --unprocessed-only --limit 100 --to-database

# Check processing statistics
python abstract_qa.py --unprocessed-only --stats-only
```

**Database Storage Benefits**:
- Q&A pairs are stored with full traceability to source documents
- Generation parameters are tracked for reproducibility
- Data is immediately available for machine learning training
- No intermediate file processing required
- Automatic detection of unprocessed documents prevents duplicates
- Efficient processing of large document collections

**Database Storage Process**:
1. Retrieves documents from the `document` table (by date range or unprocessed only)
2. Generates Q&A pairs using AI (Ollama)
3. Stores generation parameters in `generation_params` table
4. Saves each Q&A pair in `generated_data` table with JSON format:
   ```json
   {"question": "What is the effect of...", "answer": "The study found..."}
   ```

**Unprocessed Documents Mode**:
- `--unprocessed-only` flag selects documents without existing AQA entries
- Documents are ordered by ID descending (newest first)
- Prevents duplicate processing and enables incremental workflows
- Shows processing statistics: total, processed, unprocessed, and average Q&A pairs per document

**Testing Database Storage**:
```bash
# Test the database storage functionality
python test_database_storage.py

# Test unprocessed documents functionality
python test_unprocessed_documents.py

# Test Q&A export functionality
python test_qa_export.py
```

## Database Export Mode (New Feature)

The `export_qa_from_database.py` script allows you to export stored Q&A pairs back to JSONL format for training:

```bash
# Export Q&A pairs in phi3 format (default)
python export_qa_from_database.py --limit 100 --format phi3

# Export in OpenAI chat format
python export_qa_from_database.py --limit 50 --format openai

# Export in simple prompt/completion format
python export_qa_from_database.py --limit 200 --format simple

# List available filters (generation types, parameters, models)
python export_qa_from_database.py --list-filters

# Export with filters
python export_qa_from_database.py --generation-params-id 1 --limit 100
```

**Export Formats**:
- **phi3**: MLX training format with `<|user|>` and `<|assistant|>` tags
- **openai**: OpenAI chat format with messages array
- **simple**: Basic prompt/completion format

**Export Benefits**:
- Filter by generation parameters, models, or generation types
- Multiple output formats for different training frameworks
- Maintains traceability to source documents
- Efficient batch export of training data

## PostgreSQL tsquery Training Data Generation (New Feature)

The `generate_tsquery_training_data.py` script creates synthetic training data for fine-tuning models to generate optimized PostgreSQL full-text search queries:

```bash
# Generate tsquery training data from Q&A pairs
python generate_tsquery_training_data.py --limit 100 --format phi3

# Test the tsquery generation logic
python generate_tsquery_training_data.py --test

# Generate with different model
python generate_tsquery_training_data.py --limit 50 --model mistral-small3.2

# Test the functionality
python test_tsquery_training.py
```

**Training Goal**: Convert natural language medical questions into optimized PostgreSQL tsquery expressions.

**Example Transformation**:
- **Input**: "What is the cutoff value in ONSD measured by ultrasound for raised ICP?"
- **Output**: `(ONSD | "optic nerve sheath diameter") & ultrasound & (ICP | "intracranial pressure") & (cutoff | threshold)`

**Key Features**:
- Medical abbreviation expansion (MI → myocardial infarction)
- Synonym inclusion for measurement terms (cutoff → threshold)
- Proper tsquery syntax with &, |, and quotes
- Avoids over-specification that would miss relevant results
- Handles complex medical terminology and concepts

### Running the Application

```bash
# From the project root directory
python app/qas_reviewer_app.py
```

## User Interface

### Main Window

The application window is divided into several sections:

1. **Header**: Contains the application title and file input with browse functionality
2. **Status Bar**: Shows current file status and navigation information
3. **Left Panel**: 📄 Publication Abstract (scrollable markdown display)
4. **Visual Splitter**: Resizable divider between panels
5. **Right Panel**: ✏️ Q/A/S Editor (editable question and answer fields, source display, and save button)

### Navigation

- **Previous/Next Buttons**: Navigate between Q/A/S entries
- **Entry Counter**: Shows current position (e.g., "3 / 25")
- **File Status**: Displays loaded file name and total entries

### Editing

- **Question Field**: Multi-line text field for editing questions
- **Answer Field**: Multi-line text field for editing answers
- **Source Field**: Read-only field showing DOI or PMID
- **Save Button**: Becomes active when changes are made

## Workflow

### Basic Usage

1. **Launch Application**: Run the application script
2. **Load File**:
   - **Option A**: Type or paste the file path in the text field and press Enter or click "Load File"
   - **Option B**: Click "Browse" to open system file dialog (works on macOS, Linux, Windows)
   - **Option C**: Try the sample file by entering: `examples/sample_qas.txt`
3. **Review Workflow**:
   - **Left Panel**: Read the publication abstract and metadata to understand the context
   - **Right Panel**: Edit the question and answer based on the abstract content
   - **Navigation**: Use Previous/Next buttons to move between entries
4. **Edit Content**: Modify questions and answers as needed
5. **Save Changes**: Click "Save Changes" to save to a new file

### Optimized Layout

The new layout is designed for an efficient review workflow:
- **Abstract on Left**: Read the source material first to understand context
- **Editor on Right**: Make edits while keeping the abstract visible
- **Scrollable Abstract**: Handle long abstracts without losing editor space
- **Visual Separation**: Clear distinction between reading and editing areas

### File Management

- **Original Files**: Never modified, always preserved
- **Edited Files**: Saved with 'he_' prefix (e.g., `he_original_file.txt`)
- **Change Tracking**: Save button shows "*" when unsaved changes exist

### Abstract Display

- **Automatic Loading**: Abstracts are automatically fetched when navigating
- **Database Integration**: Uses DOI or PMID to query PostgreSQL database
- **Markdown Formatting**: Displays title, metadata, and abstract in formatted view
- **Scrollable Content**: Long abstracts can be scrolled within the left panel
- **Enhanced Styling**: Color-coded panels with icons for better visual organization

## Error Handling

### Common Issues

1. **Database Connection Errors**:
   - Check environment variables
   - Verify PostgreSQL server is running
   - Ensure database and table exist

2. **File Format Errors**:
   - Verify Q/A/S format is correct
   - Check for missing Q:, A:, or S: prefixes
   - Ensure entries are separated by blank lines

3. **Permission Errors**:
   - Check file read/write permissions
   - Ensure output directory is writable

### Error Messages

The application provides user-friendly error messages through:
- Status text updates (red for errors, green for success)
- Detailed logging to `qas_reviewer.log`
- Console output for debugging

## Architecture

### Components

1. **QASEntry Class**: Represents individual Q/A/S entries with change tracking
2. **QASReviewerApp Class**: Main application logic and UI management
3. **DatabaseHelper Class**: Database connection and query utilities
4. **UI Components**: Flet-based user interface elements

### Database Integration

The application uses the `helpers.database.DatabaseHelper` class to:
- Connect to PostgreSQL database
- Query documents by DOI or PMID
- Retrieve title, abstract, and metadata
- Handle connection errors gracefully

### File Processing

- **Parsing**: Robust parsing of Q/A/S format with error handling
- **Validation**: Checks for complete entries and proper format
- **Saving**: Preserves format while allowing modifications

## Testing

### Unit Tests

Run the test suite:

```bash
# Test database helper
python -m pytest tests/test_database_helper.py

# Test Q/A/S parsing
python -m pytest tests/test_qas_parser.py

# Run all tests
python -m pytest tests/
```

### Manual Testing

1. Create test Q/A/S files with various formats
2. Test database connectivity with different configurations
3. Verify file saving and loading functionality
4. Test error handling with invalid inputs

## Troubleshooting

### Performance Issues

- **Large Files**: Files >10MB may cause performance issues
- **Database Queries**: Slow abstract loading may indicate database performance issues
- **Memory Usage**: Monitor memory usage with very large datasets

### UI Issues

- **Window Sizing**: Adjust window dimensions in the code if needed
- **Font Rendering**: Ensure system fonts support the content
- **Color Themes**: Colors may appear differently on different systems

### Logging

Check the log file `qas_reviewer.log` for detailed error information:

```bash
tail -f qas_reviewer.log
```

## Future Enhancements

### Planned Features

1. **Batch Operations**: Edit multiple entries simultaneously
2. **Search Functionality**: Find specific entries by content
3. **Export Options**: Export to different formats (JSON, CSV)
4. **Undo/Redo**: Support for undoing changes
5. **Keyboard Shortcuts**: Improve navigation efficiency

### Configuration Options

1. **UI Themes**: Light/dark mode support
2. **Font Settings**: Customizable fonts and sizes
3. **Database Settings**: Multiple database connections
4. **File Associations**: Register as default handler for Q/A/S files

## API Reference

### QASEntry

```python
class QASEntry:
    def __init__(self, question: str, answer: str, source: str)
    def mark_modified(self) -> None
    def is_changed(self) -> bool
```

### DatabaseHelper

```python
class DatabaseHelper:
    def connect(self) -> bool
    def disconnect(self) -> None
    def get_abstract_by_doi(self, doi: str) -> Optional[Dict[str, Any]]
    def get_abstract_by_pmid(self, pmid: str) -> Optional[Dict[str, Any]]
    def get_abstract_by_source(self, source: str) -> Optional[Dict[str, Any]]
```

### QASReviewerApp

```python
class QASReviewerApp:
    def load_qas_file(self, file_path: Path) -> None
    def parse_qas_file(self, file_path: Path) -> List[QASEntry]
    def save_changes(self, e) -> None
    def display_current_entry(self) -> None
```
