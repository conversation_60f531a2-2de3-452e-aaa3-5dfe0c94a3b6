#!/usr/bin/env python3
"""
Generate PostgreSQL tsquery Training Data

This script fetches Q&A pairs from the database and generates synthetic training data
for fine-tuning a model to create optimized PostgreSQL full-text search queries.

The model learns to convert natural language medical questions into tsquery expressions
that would likely retrieve documents containing the answers.
"""

import os
import json
import logging
import argparse
import re
from typing import List, Dict, Set, Optional, Tuple
import psycopg2
from psycopg2 import Error
from dotenv import load_dotenv
from datetime import datetime
import ollama

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'tsquery_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class TsqueryTrainingGenerator:
    """
    Generates training data for PostgreSQL tsquery optimization.
    """

    def __init__(self, model_name: str = "mistral-small3.2"):
        """Initialize with database connection and AI model."""
        self.model_name = model_name
        self.connection = None
        self.cursor = None
        
        # Medical term expansions and synonyms
        self.medical_synonyms = {
            'MI': ['myocardial infarction', 'heart attack'],
            'ICP': ['intracranial pressure'],
            'ONSD': ['optic nerve sheath diameter'],
            'CT': ['computed tomography', 'CAT scan'],
            'MRI': ['magnetic resonance imaging'],
            'ECG': ['electrocardiogram', 'EKG'],
            'BP': ['blood pressure'],
            'HR': ['heart rate'],
            'CVD': ['cardiovascular disease'],
            'CHF': ['congestive heart failure', 'heart failure'],
            'COPD': ['chronic obstructive pulmonary disease'],
            'DM': ['diabetes mellitus', 'diabetes'],
            'HTN': ['hypertension'],
            'AF': ['atrial fibrillation'],
            'PE': ['pulmonary embolism'],
            'DVT': ['deep vein thrombosis'],
            'ICU': ['intensive care unit'],
            'ED': ['emergency department', 'emergency room'],
            'OR': ['operating room', 'operating theatre'],
            'ICH': ['intracranial hemorrhage', 'brain hemorrhage'],
            'SAH': ['subarachnoid hemorrhage'],
            'TBI': ['traumatic brain injury'],
            'GCS': ['glasgow coma scale'],
            'CPR': ['cardiopulmonary resuscitation'],
            'ROSC': ['return of spontaneous circulation']
        }
        
        # Common medical measurement terms
        self.measurement_terms = {
            'cutoff': ['cut-off', 'threshold', 'limit', 'value'],
            'sensitivity': ['detection rate'],
            'specificity': ['true negative rate'],
            'accuracy': ['precision'],
            'mortality': ['death rate', 'fatality'],
            'morbidity': ['complication rate'],
            'prevalence': ['frequency', 'occurrence'],
            'incidence': ['new cases'],
            'efficacy': ['effectiveness'],
            'dosage': ['dose', 'amount'],
            'duration': ['time', 'period'],
            'outcome': ['result', 'endpoint']
        }
        
        # Database connection setup
        self.db_config = {
            'host': os.getenv('POSTGRES_HOST'),
            'port': os.getenv('POSTGRES_PORT', 5432),
            'database': os.getenv('POSTGRES_DB'),
            'user': os.getenv('POSTGRES_USER'),
            'password': os.getenv('POSTGRES_PASSWORD')
        }

    def connect(self):
        """Establish database connection."""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info(f"Connected to database: {self.db_config['database']}")
        except Error as e:
            logger.error(f"Database connection error: {e}")
            raise

    def disconnect(self):
        """Close database connection."""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("Database connection closed")

    def fetch_qa_pairs(self, limit: int = 100) -> List[Dict]:
        """Fetch Q&A pairs from the database."""
        try:
            query = """
                SELECT gd.data, d.title, d.abstract
                FROM generated_data gd
                JOIN document d ON gd.document_id = d.id
                JOIN generated_type gt ON gd.generation_type = gt.id
                WHERE gt.type_name = 'AQA'
                ORDER BY gd.id DESC
                LIMIT %s
            """
            
            self.cursor.execute(query, (limit,))
            results = self.cursor.fetchall()
            
            qa_pairs = []
            for row in results:
                try:
                    qa_data = json.loads(row[0])
                    qa_pairs.append({
                        'question': qa_data.get('question', ''),
                        'answer': qa_data.get('answer', ''),
                        'title': row[1],
                        'abstract': row[2]
                    })
                except json.JSONDecodeError:
                    continue
            
            logger.info(f"Fetched {len(qa_pairs)} Q&A pairs")
            return qa_pairs
            
        except Error as e:
            logger.error(f"Error fetching Q&A pairs: {e}")
            raise

    def extract_key_terms(self, text: str) -> Set[str]:
        """Extract key medical terms from text."""
        # Convert to lowercase for processing
        text_lower = text.lower()
        
        # Remove common stop words and extract meaningful terms
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'what', 'which', 'who', 'when', 'where', 'why', 'how'}
        
        # Extract words and phrases
        words = re.findall(r'\b[a-zA-Z]+\b', text_lower)
        terms = set()
        
        # Add individual meaningful words
        for word in words:
            if len(word) > 2 and word not in stop_words:
                terms.add(word)
        
        # Add medical abbreviations (preserve case)
        abbrev_pattern = r'\b[A-Z]{2,}\b'
        abbreviations = re.findall(abbrev_pattern, text)
        terms.update(abbreviations)
        
        # Add quoted phrases
        quoted_phrases = re.findall(r'"([^"]+)"', text)
        terms.update(quoted_phrases)
        
        return terms

    def generate_tsquery_with_ai(self, question: str, answer: str, title: str) -> str:
        """Use AI to generate an optimized tsquery expression."""
        
        prompt = f"""You are an expert in PostgreSQL full-text search optimization for medical literature.

Given a medical question and its answer, create an optimized tsquery expression that would likely retrieve documents containing this information.

Rules for tsquery syntax:
1. Use & for AND operations
2. Use | for OR operations  
3. Use quotes for exact phrases: "exact phrase"
4. Use parentheses for grouping: (term1 | term2) & term3
5. Escape single quotes by doubling them: 'patient''s condition'
6. Include medical abbreviations and their expansions: (MI | "myocardial infarction")
7. Add relevant synonyms and related terms
8. Include measurement-related terms when appropriate (cutoff, threshold, sensitivity, etc.)
9. Avoid overly specific terms that would exclude relevant results
10. Focus on terms that would appear in medical abstracts

Question: {question}
Answer: {answer}
Document Title: {title}

Generate ONLY the tsquery expression (the part that goes inside plainto_tsquery or to_tsquery), without any SQL syntax or quotes around the whole expression.

Example format: (ONSD | "optic nerve sheath diameter") & ultrasound & (ICP | "intracranial pressure") & (cutoff | threshold)

Tsquery expression:"""

        try:
            response = ollama.chat(
                model=self.model_name,
                messages=[{'role': 'user', 'content': prompt}]
            )
            
            tsquery = response['message']['content'].strip()
            
            # Clean up the response - remove any extra quotes or SQL syntax
            tsquery = re.sub(r'^["\']|["\']$', '', tsquery)
            tsquery = re.sub(r'^to_tsquery\(.*?\,\s*["\']|["\'].*?\)$', '', tsquery)
            tsquery = re.sub(r'^plainto_tsquery\(.*?\,\s*["\']|["\'].*?\)$', '', tsquery)
            
            return tsquery.strip()
            
        except Exception as e:
            logger.warning(f"AI generation failed: {e}")
            return self.generate_basic_tsquery(question, answer)

    def generate_basic_tsquery(self, question: str, answer: str) -> str:
        """Generate a basic tsquery using rule-based approach as fallback."""
        
        # Extract key terms from question and answer
        question_terms = self.extract_key_terms(question)
        answer_terms = self.extract_key_terms(answer)
        
        # Combine and prioritize terms
        all_terms = question_terms.union(answer_terms)
        
        # Build tsquery components
        components = []
        
        # Add medical abbreviations with expansions
        for term in all_terms:
            if term.upper() in self.medical_synonyms:
                expansions = self.medical_synonyms[term.upper()]
                expansion_group = f'({term.upper()} | "' + '" | "'.join(expansions) + '")'
                components.append(expansion_group)
            elif len(term) > 3 and term.isalpha():
                components.append(term)
        
        # Add measurement terms if relevant
        for measure_term, synonyms in self.measurement_terms.items():
            if any(measure_term in text.lower() for text in [question, answer]):
                synonym_group = f'({measure_term} | "' + '" | "'.join(synonyms) + '")'
                components.append(synonym_group)
        
        # Join with AND operators
        if components:
            return ' & '.join(components[:6])  # Limit to 6 components to avoid overly complex queries
        else:
            return 'medical & research'  # Fallback

    def generate_training_data(self, qa_pairs: List[Dict], output_format: str = "phi3") -> List[Dict]:
        """Generate training data for tsquery optimization."""
        
        training_data = []
        
        for i, qa_pair in enumerate(qa_pairs, 1):
            logger.info(f"Processing Q&A pair {i}/{len(qa_pairs)}")
            
            question = qa_pair['question']
            answer = qa_pair['answer']
            title = qa_pair['title']
            
            # Generate tsquery expression
            tsquery = self.generate_tsquery_with_ai(question, answer, title)
            
            # Create training example
            if output_format == "phi3":
                training_example = {
                    "text": f"<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n{question} <|end|>\n<|assistant|> \n{tsquery} <|end|>"
                }
            elif output_format == "openai":
                training_example = {
                    "messages": [
                        {"role": "user", "content": f"Convert this medical query to a PostgreSQL tsquery expression:\n{question}"},
                        {"role": "assistant", "content": tsquery}
                    ]
                }
            else:  # simple format
                training_example = {
                    "prompt": f"Convert this medical query to a PostgreSQL tsquery expression:\n{question}",
                    "completion": tsquery
                }
            
            training_data.append(training_example)
            
            # Add some delay to avoid overwhelming the AI API
            if i % 10 == 0:
                logger.info(f"Generated {i} training examples...")
        
        return training_data

    def save_training_data(self, training_data: List[Dict], output_file: str):
        """Save training data to JSONL file."""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for example in training_data:
                    json_line = json.dumps(example, ensure_ascii=False)
                    f.write(json_line + '\n')
            
            logger.info(f"Saved {len(training_data)} training examples to {output_file}")
            
        except Exception as e:
            logger.error(f"Error saving training data: {e}")
            raise

def main():
    """Main function to generate tsquery training data."""
    parser = argparse.ArgumentParser(description='Generate PostgreSQL tsquery training data from Q&A pairs')
    parser.add_argument('--limit', type=int, default=100, help='Number of Q&A pairs to process (default: 100)')
    parser.add_argument('--output', type=str, help='Output JSONL file (default: auto-generated)')
    parser.add_argument('--format', type=str, choices=['phi3', 'openai', 'simple'], default='phi3',
                       help='Output format (default: phi3)')
    parser.add_argument('--model', type=str, default='mistral-small3.2', help='Ollama model for tsquery generation')
    
    args = parser.parse_args()
    
    # Generate output filename if not provided
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"tsquery_training_{args.format}_{timestamp}.jsonl"
    
    generator = TsqueryTrainingGenerator(model_name=args.model)
    
    try:
        # Connect to database
        generator.connect()
        
        # Fetch Q&A pairs
        qa_pairs = generator.fetch_qa_pairs(limit=args.limit)
        
        if not qa_pairs:
            logger.error("No Q&A pairs found in database")
            return
        
        # Generate training data
        logger.info(f"Generating tsquery training data using model: {args.model}")
        training_data = generator.generate_training_data(qa_pairs, args.format)
        
        # Save training data
        generator.save_training_data(training_data, args.output)
        
        logger.info(f"\n✅ Training data generation completed!")
        logger.info(f"📁 Output file: {args.output}")
        logger.info(f"📊 Training examples: {len(training_data)}")
        logger.info(f"🎯 Format: {args.format}")
        
        # Show sample
        if training_data:
            logger.info(f"\n📝 Sample training example:")
            sample = training_data[0]
            logger.info(f"   {json.dumps(sample, indent=2, ensure_ascii=False)[:300]}...")
        
    except Exception as e:
        logger.error(f"Training data generation failed: {e}")
        raise
    finally:
        generator.disconnect()

def test_tsquery_generation():
    """Test the tsquery generation with sample data."""

    generator = TsqueryTrainingGenerator()

    # Test cases
    test_cases = [
        {
            "question": "What is the cutoff value in ONSD measured by ultrasound for raised ICP?",
            "answer": "The cutoff value for ONSD measured by ultrasound for detecting raised ICP is typically 5.0-5.7 mm.",
            "title": "Optic nerve sheath diameter measurement for intracranial pressure assessment"
        },
        {
            "question": "What is the sensitivity of troponin for diagnosing myocardial infarction?",
            "answer": "Troponin has a sensitivity of 95-99% for diagnosing myocardial infarction when measured 6-12 hours after symptom onset.",
            "title": "Cardiac biomarkers in acute coronary syndrome"
        },
        {
            "question": "How effective is CPR in achieving ROSC in cardiac arrest patients?",
            "answer": "CPR achieves return of spontaneous circulation (ROSC) in approximately 25-30% of out-of-hospital cardiac arrest cases.",
            "title": "Outcomes of cardiopulmonary resuscitation in cardiac arrest"
        }
    ]

    logger.info("=== Testing tsquery generation ===")

    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\nTest case {i}:")
        logger.info(f"Question: {test_case['question']}")

        # Test AI generation
        try:
            ai_tsquery = generator.generate_tsquery_with_ai(
                test_case['question'],
                test_case['answer'],
                test_case['title']
            )
            logger.info(f"AI tsquery: {ai_tsquery}")
        except Exception as e:
            logger.warning(f"AI generation failed: {e}")

        # Test basic generation
        basic_tsquery = generator.generate_basic_tsquery(
            test_case['question'],
            test_case['answer']
        )
        logger.info(f"Basic tsquery: {basic_tsquery}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_tsquery_generation()
    else:
        main()
