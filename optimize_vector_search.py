#!/usr/bin/env python3
"""
Vector Search Optimization Tool

This script helps diagnose and optimize vector search performance issues.
It checks indexes, analyzes query plans, and provides optimization recommendations.
"""

import os
import sys
import logging
import argparse
import time
from typing import Dict, List
import psycopg2
from psycopg2 import sql, Error
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class VectorSearchOptimizer:
    """
    Analyzes and optimizes vector search performance.
    """

    def __init__(self):
        """Initialize database connection."""
        self.connection = None
        self.cursor = None
        
        # Database connection setup
        self.db_config = {
            'host': os.getenv('POSTGRES_HOST'),
            'port': os.getenv('POSTGRES_PORT', 5432),
            'database': os.getenv('POSTGRES_DB'),
            'user': os.getenv('POSTGRES_USER'),
            'password': os.getenv('POSTGRES_PASSWORD')
        }

    def connect(self):
        """Establish database connection."""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            logger.info(f"Connected to database: {self.db_config['database']}")
        except Error as e:
            logger.error(f"Database connection error: {e}")
            raise

    def disconnect(self):
        """Close database connection."""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()

    def check_indexes(self) -> Dict:
        """Check existing indexes on the embedding table."""
        try:
            logger.info("=== Checking Indexes ===")
            
            # Check for indexes on emb_1024 table
            index_query = """
                SELECT 
                    indexname,
                    indexdef,
                    tablename
                FROM pg_indexes 
                WHERE tablename = 'emb_1024'
                ORDER BY indexname;
            """
            
            self.cursor.execute(index_query)
            indexes = self.cursor.fetchall()
            
            index_info = {
                'total_indexes': len(indexes),
                'has_hnsw': False,
                'has_ivfflat': False,
                'indexes': []
            }
            
            for idx in indexes:
                index_name, index_def, table_name = idx
                index_info['indexes'].append({
                    'name': index_name,
                    'definition': index_def
                })
                
                if 'hnsw' in index_def.lower():
                    index_info['has_hnsw'] = True
                if 'ivfflat' in index_def.lower():
                    index_info['has_ivfflat'] = True
            
            # Display results
            logger.info(f"Found {len(indexes)} indexes on emb_1024:")
            for idx in indexes:
                logger.info(f"  - {idx[0]}: {idx[1]}")
            
            if not index_info['has_hnsw'] and not index_info['has_ivfflat']:
                logger.warning("⚠️ No vector indexes found! This will cause slow searches.")
                logger.info("Consider creating: CREATE INDEX ON emb_1024 USING hnsw (embedding vector_cosine_ops);")
            
            return index_info
            
        except Error as e:
            logger.error(f"Error checking indexes: {e}")
            return {}

    def analyze_table_stats(self) -> Dict:
        """Analyze table statistics that affect performance."""
        try:
            logger.info("\n=== Analyzing Table Statistics ===")
            
            stats = {}
            
            # Get table sizes
            size_query = """
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    correlation
                FROM pg_stats 
                WHERE tablename IN ('emb_1024', 'chunks', 'document')
                ORDER BY tablename, attname;
            """
            
            self.cursor.execute(size_query)
            pg_stats = self.cursor.fetchall()
            
            # Get table row counts and sizes
            tables_info = []
            for table in ['emb_1024', 'chunks', 'document']:
                try:
                    # Row count
                    self.cursor.execute(f"SELECT COUNT(*) FROM {table};")
                    row_count = self.cursor.fetchone()[0]
                    
                    # Table size
                    self.cursor.execute(f"SELECT pg_size_pretty(pg_total_relation_size('{table}'));")
                    table_size = self.cursor.fetchone()[0]
                    
                    tables_info.append({
                        'table': table,
                        'rows': row_count,
                        'size': table_size
                    })
                    
                    logger.info(f"{table}: {row_count:,} rows, {table_size}")
                    
                except Error as e:
                    logger.warning(f"Could not get stats for {table}: {e}")
            
            stats['tables'] = tables_info
            stats['pg_stats'] = pg_stats
            
            return stats
            
        except Error as e:
            logger.error(f"Error analyzing table stats: {e}")
            return {}

    def test_query_performance(self, test_embedding: List[float], limits: List[int] = [10, 50, 100]) -> Dict:
        """Test query performance with different approaches."""
        try:
            logger.info("\n=== Testing Query Performance ===")
            
            # Convert embedding to string format
            embedding_str = '[' + ','.join(map(str, test_embedding)) + ']'
            
            performance_results = {}
            
            # Test different query approaches
            queries = {
                'original': """
                    SELECT 
                        c.document_id,
                        d.title,
                        e.embedding <=> %s::vector as distance
                    FROM emb_1024 e
                    JOIN chunks c ON e.chunk_id = c.id
                    JOIN document d ON c.document_id = d.id
                    ORDER BY e.embedding <=> %s::vector
                    LIMIT %s;
                """,
                'optimized_cte': """
                    WITH similar_embeddings AS (
                        SELECT 
                            chunk_id,
                            embedding <=> %s::vector as distance
                        FROM emb_1024
                        ORDER BY embedding <=> %s::vector
                        LIMIT %s
                    )
                    SELECT 
                        c.document_id,
                        d.title,
                        se.distance
                    FROM similar_embeddings se
                    JOIN chunks c ON se.chunk_id = c.id
                    JOIN document d ON c.document_id = d.id
                    ORDER BY se.distance;
                """,
                'embedding_only': """
                    SELECT 
                        chunk_id,
                        embedding <=> %s::vector as distance
                    FROM emb_1024
                    ORDER BY embedding <=> %s::vector
                    LIMIT %s;
                """
            }
            
            for query_name, query in queries.items():
                logger.info(f"\nTesting {query_name} query:")
                query_results = {}
                
                for limit in limits:
                    try:
                        start_time = time.time()
                        
                        if query_name == 'embedding_only':
                            self.cursor.execute(query, (embedding_str, embedding_str, limit))
                        else:
                            self.cursor.execute(query, (embedding_str, embedding_str, limit))
                        
                        results = self.cursor.fetchall()
                        end_time = time.time()
                        
                        duration = end_time - start_time
                        query_results[limit] = {
                            'duration': duration,
                            'result_count': len(results)
                        }
                        
                        logger.info(f"  Limit {limit:3d}: {duration:.3f}s ({len(results)} results)")
                        
                    except Error as e:
                        logger.error(f"  Limit {limit}: Error - {e}")
                        query_results[limit] = {'error': str(e)}
                
                performance_results[query_name] = query_results
            
            return performance_results
            
        except Exception as e:
            logger.error(f"Error testing query performance: {e}")
            return {}

    def analyze_query_plan(self, test_embedding: List[float]) -> str:
        """Analyze the query execution plan."""
        try:
            logger.info("\n=== Analyzing Query Execution Plan ===")
            
            embedding_str = '[' + ','.join(map(str, test_embedding)) + ']'
            
            # Get execution plan for the optimized query
            explain_query = """
                EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT)
                WITH similar_embeddings AS (
                    SELECT 
                        chunk_id,
                        embedding <=> %s::vector as distance
                    FROM emb_1024
                    ORDER BY embedding <=> %s::vector
                    LIMIT 20
                )
                SELECT 
                    c.document_id,
                    d.title,
                    se.distance
                FROM similar_embeddings se
                JOIN chunks c ON se.chunk_id = c.id
                JOIN document d ON c.document_id = d.id
                ORDER BY se.distance;
            """
            
            self.cursor.execute(explain_query, (embedding_str, embedding_str))
            plan_rows = self.cursor.fetchall()
            
            plan_text = '\n'.join([row[0] for row in plan_rows])
            logger.info("Query execution plan:")
            logger.info(plan_text)
            
            return plan_text
            
        except Error as e:
            logger.error(f"Error analyzing query plan: {e}")
            return ""

    def get_optimization_recommendations(self, index_info: Dict, stats: Dict, performance: Dict) -> List[str]:
        """Generate optimization recommendations based on analysis."""
        recommendations = []
        
        # Index recommendations
        if not index_info.get('has_hnsw') and not index_info.get('has_ivfflat'):
            recommendations.append(
                "🔥 CRITICAL: Create a vector index for fast similarity search:\n"
                "   CREATE INDEX ON emb_1024 USING hnsw (embedding vector_cosine_ops);"
            )
        
        # Table size recommendations
        for table_info in stats.get('tables', []):
            if table_info['table'] == 'emb_1024' and table_info['rows'] > 100000:
                recommendations.append(
                    f"📊 Large embedding table ({table_info['rows']:,} rows): "
                    "Consider tuning HNSW parameters (m, ef_construction)"
                )
        
        # Performance recommendations
        if performance:
            # Check if embedding-only query is much faster
            embedding_times = performance.get('embedding_only', {})
            optimized_times = performance.get('optimized_cte', {})
            
            if embedding_times and optimized_times:
                for limit in [20, 50]:
                    if (limit in embedding_times and limit in optimized_times and
                        'duration' in embedding_times[limit] and 'duration' in optimized_times[limit]):
                        
                        embedding_time = embedding_times[limit]['duration']
                        optimized_time = optimized_times[limit]['duration']
                        
                        if optimized_time > embedding_time * 3:
                            recommendations.append(
                                "⚡ Consider separating vector search from metadata joins:\n"
                                "   1. First get chunk_ids with vector search\n"
                                "   2. Then join with metadata tables"
                            )
                            break
        
        # General recommendations
        recommendations.extend([
            "🔧 Ensure PostgreSQL shared_buffers is set appropriately (25% of RAM)",
            "🔧 Consider increasing work_mem for complex queries",
            "🔧 Run VACUUM ANALYZE on tables regularly",
            "🔧 Monitor pg_stat_user_indexes for index usage"
        ])
        
        return recommendations

def main():
    """Main function to run optimization analysis."""
    parser = argparse.ArgumentParser(description='Optimize vector search performance')
    parser.add_argument('--test-embedding', action='store_true', help='Run performance tests with sample embedding')
    parser.add_argument('--explain', action='store_true', help='Show query execution plan')
    parser.add_argument('--recommendations', action='store_true', help='Show optimization recommendations')
    
    args = parser.parse_args()
    
    optimizer = VectorSearchOptimizer()
    
    try:
        optimizer.connect()
        
        # Check indexes
        index_info = optimizer.check_indexes()
        
        # Analyze table statistics
        stats = optimizer.analyze_table_stats()
        
        performance = {}
        
        # Test performance if requested
        if args.test_embedding:
            # Create a sample embedding for testing
            test_embedding = [0.1] * 1024  # Simple test vector
            performance = optimizer.test_query_performance(test_embedding)
        
        # Show execution plan if requested
        if args.explain and args.test_embedding:
            test_embedding = [0.1] * 1024
            optimizer.analyze_query_plan(test_embedding)
        
        # Show recommendations
        if args.recommendations or not any([args.test_embedding, args.explain]):
            recommendations = optimizer.get_optimization_recommendations(index_info, stats, performance)
            
            logger.info("\n=== Optimization Recommendations ===")
            for i, rec in enumerate(recommendations, 1):
                logger.info(f"{i}. {rec}")
        
    except Exception as e:
        logger.error(f"Optimization analysis failed: {e}")
        sys.exit(1)
    finally:
        optimizer.disconnect()

if __name__ == "__main__":
    main()
