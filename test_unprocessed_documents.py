#!/usr/bin/env python3
"""
Test script for unprocessed documents functionality in abstract_qa.py
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the current directory to the path so we can import abstract_qa
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from abstract_qa import AbstractQARetriever

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_unprocessed_documents():
    """Test the unprocessed documents functionality."""
    
    # Initialize the retriever
    retriever = AbstractQARetriever(model_name="mistral-small3.2")
    
    try:
        # Connect to database
        retriever.connect()
        logger.info("Database connection successful")
        
        # Get processing statistics
        logger.info("\n=== Processing Statistics ===")
        stats = retriever.get_processing_stats()
        logger.info(f"Total documents (with title/abstract): {stats['total_documents']:,}")
        logger.info(f"Processed documents (with AQA): {stats['processed_documents']:,}")
        logger.info(f"Unprocessed documents: {stats['unprocessed_documents']:,}")
        logger.info(f"Total AQA pairs generated: {stats['total_aqa_pairs']:,}")
        logger.info(f"Average Q&A pairs per document: {stats['avg_qa_per_document']}")
        
        if stats['unprocessed_documents'] == 0:
            logger.info("🎉 All documents have been processed!")
            return True
        
        # Get a small sample of unprocessed documents
        logger.info(f"\n=== Sample Unprocessed Documents ===")
        unprocessed_docs = retriever.get_unprocessed_documents(limit=5)
        
        if not unprocessed_docs:
            logger.warning("No unprocessed documents found")
            return False
        
        logger.info(f"Retrieved {len(unprocessed_docs)} unprocessed documents:")
        for i, doc in enumerate(unprocessed_docs, 1):
            logger.info(f"  {i}. ID:{doc['id']} - {doc['publication_date']} - {doc['title'][:80]}...")
        
        # Verify these documents don't have AQA entries
        logger.info(f"\n=== Verification ===")
        for doc in unprocessed_docs[:3]:  # Check first 3
            retriever.cursor.execute("""
                SELECT COUNT(*) 
                FROM generated_data gd
                JOIN generated_type gt ON gd.generation_type = gt.id
                WHERE gt.type_name = 'AQA' AND gd.document_id = %s
            """, (doc['id'],))
            
            count = retriever.cursor.fetchone()[0]
            if count == 0:
                logger.info(f"✅ Document ID {doc['id']} has no AQA entries (correct)")
            else:
                logger.error(f"❌ Document ID {doc['id']} has {count} AQA entries (should be 0)")
                return False
        
        logger.info("✅ Unprocessed documents test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Unprocessed documents test FAILED with error: {e}")
        return False
    finally:
        retriever.disconnect()

def test_document_ordering():
    """Test that unprocessed documents are returned in correct order (newest first by ID)."""
    
    retriever = AbstractQARetriever()
    
    try:
        retriever.connect()
        
        # Get unprocessed documents
        documents = retriever.get_unprocessed_documents(limit=10)
        
        if len(documents) < 2:
            logger.info("Not enough unprocessed documents to test ordering")
            return True
        
        # Check that documents are ordered by ID descending (newest first)
        for i in range(len(documents) - 1):
            current_id = documents[i]['id']
            next_id = documents[i + 1]['id']
            
            if current_id <= next_id:
                logger.error(f"❌ Documents not properly ordered: ID {current_id} should be > ID {next_id}")
                return False
        
        logger.info(f"✅ Document ordering test PASSED - {len(documents)} documents properly ordered by ID DESC")
        return True
        
    except Exception as e:
        logger.error(f"❌ Document ordering test FAILED with error: {e}")
        return False
    finally:
        retriever.disconnect()

def test_query_performance():
    """Test the performance of the unprocessed documents query."""
    
    retriever = AbstractQARetriever()
    
    try:
        retriever.connect()
        
        import time
        
        # Test with different limits to see performance
        limits = [10, 50, 100, 500]
        
        logger.info("\n=== Query Performance Test ===")
        for limit in limits:
            start_time = time.time()
            documents = retriever.get_unprocessed_documents(limit=limit)
            end_time = time.time()
            
            duration = end_time - start_time
            logger.info(f"Limit {limit:3d}: {len(documents):3d} docs retrieved in {duration:.3f}s")
            
            if duration > 5.0:  # If query takes more than 5 seconds
                logger.warning(f"⚠️ Query with limit {limit} took {duration:.3f}s - consider adding database indexes")
        
        logger.info("✅ Query performance test completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Query performance test FAILED with error: {e}")
        return False
    finally:
        retriever.disconnect()

if __name__ == "__main__":
    logger.info("Starting unprocessed documents tests...")
    
    # Test 1: Basic functionality
    logger.info("\n=== Test 1: Basic Functionality ===")
    basic_ok = test_unprocessed_documents()
    
    # Test 2: Document ordering
    logger.info("\n=== Test 2: Document Ordering ===")
    ordering_ok = test_document_ordering()
    
    # Test 3: Query performance
    logger.info("\n=== Test 3: Query Performance ===")
    performance_ok = test_query_performance()
    
    # Summary
    if basic_ok and ordering_ok and performance_ok:
        logger.info("\n🎉 All unprocessed documents tests PASSED!")
        logger.info("\nYou can now use:")
        logger.info("  python abstract_qa.py --unprocessed-only --limit 50 --to-database")
        logger.info("  python abstract_qa.py --unprocessed-only --stats-only")
    else:
        logger.error("\n💥 Some tests FAILED!")
        sys.exit(1)
