#!/usr/bin/env python3
"""
Example: PostgreSQL tsquery Training Data Generation

This example demonstrates how to generate synthetic training data for fine-tuning
a language model to create optimized PostgreSQL full-text search queries.

The goal is to train a model that can convert natural language medical questions
into effective tsquery expressions for searching medical literature.
"""

import os
import sys
import json
import logging

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from generate_tsquery_training_data import TsqueryTrainingGenerator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demonstrate_tsquery_generation():
    """Demonstrate the tsquery generation process."""
    
    logger.info("=== PostgreSQL tsquery Training Data Generation Demo ===")
    
    # Example medical questions and their context
    examples = [
        {
            "question": "What is the cutoff value in ONSD measured by ultrasound for raised ICP?",
            "answer": "The cutoff value for ONSD measured by ultrasound for detecting raised ICP is typically 5.0-5.7 mm in adults.",
            "title": "Optic nerve sheath diameter measurement for intracranial pressure assessment",
            "expected_tsquery_elements": ["ONSD", "optic nerve sheath diameter", "ultrasound", "ICP", "intracranial pressure", "cutoff", "threshold"]
        },
        {
            "question": "What is the sensitivity of troponin for diagnosing myocardial infarction?",
            "answer": "Troponin has a sensitivity of 95-99% for diagnosing myocardial infarction when measured 6-12 hours after symptom onset.",
            "title": "Cardiac biomarkers in acute coronary syndrome diagnosis",
            "expected_tsquery_elements": ["troponin", "sensitivity", "MI", "myocardial infarction", "diagnosis"]
        },
        {
            "question": "How effective is CPR in achieving ROSC in cardiac arrest patients?",
            "answer": "CPR achieves return of spontaneous circulation (ROSC) in approximately 25-30% of out-of-hospital cardiac arrest cases.",
            "title": "Outcomes of cardiopulmonary resuscitation in cardiac arrest",
            "expected_tsquery_elements": ["CPR", "cardiopulmonary resuscitation", "ROSC", "return of spontaneous circulation", "effectiveness", "cardiac arrest"]
        },
        {
            "question": "What CT findings indicate acute stroke?",
            "answer": "CT findings in acute stroke include hypodense areas, loss of gray-white matter differentiation, and early signs of infarction.",
            "title": "Neuroimaging in acute stroke diagnosis",
            "expected_tsquery_elements": ["CT", "computed tomography", "stroke", "findings", "diagnosis", "acute"]
        }
    ]
    
    generator = TsqueryTrainingGenerator()
    
    logger.info("Generating tsquery expressions for example questions...\n")
    
    for i, example in enumerate(examples, 1):
        logger.info(f"Example {i}:")
        logger.info(f"Question: {example['question']}")
        logger.info(f"Answer: {example['answer'][:100]}...")
        
        # Generate basic tsquery (rule-based)
        basic_tsquery = generator.generate_basic_tsquery(
            example['question'], 
            example['answer']
        )
        logger.info(f"Basic tsquery: {basic_tsquery}")
        
        # Show what elements we expect to see
        logger.info(f"Expected elements: {', '.join(example['expected_tsquery_elements'])}")
        
        # Analyze the generated tsquery
        found_elements = []
        for element in example['expected_tsquery_elements']:
            if element.lower() in basic_tsquery.lower():
                found_elements.append(element)
        
        logger.info(f"Found elements: {', '.join(found_elements)}")
        logger.info(f"Coverage: {len(found_elements)}/{len(example['expected_tsquery_elements'])} elements")
        logger.info("-" * 80)

def show_training_data_formats():
    """Show examples of different training data formats."""
    
    logger.info("\n=== Training Data Format Examples ===")
    
    generator = TsqueryTrainingGenerator()
    
    # Sample Q&A pair
    sample_qa = {
        "question": "What is the diagnostic accuracy of ultrasound for appendicitis?",
        "answer": "Ultrasound has 85-95% diagnostic accuracy for acute appendicitis in experienced hands.",
        "title": "Ultrasound diagnosis of acute appendicitis",
        "abstract": "This study evaluates ultrasound accuracy for appendicitis diagnosis."
    }
    
    # Generate sample tsquery
    sample_tsquery = "(ultrasound | sonography) & (appendicitis | \"acute appendicitis\") & (accuracy | sensitivity | specificity | diagnosis)"
    
    formats = {
        "phi3": {
            "text": f"<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n{sample_qa['question']} <|end|>\n<|assistant|> \n{sample_tsquery} <|end|>"
        },
        "openai": {
            "messages": [
                {"role": "user", "content": f"Convert this medical query to a PostgreSQL tsquery expression:\n{sample_qa['question']}"},
                {"role": "assistant", "content": sample_tsquery}
            ]
        },
        "simple": {
            "prompt": f"Convert this medical query to a PostgreSQL tsquery expression:\n{sample_qa['question']}",
            "completion": sample_tsquery
        }
    }
    
    for format_name, format_example in formats.items():
        logger.info(f"\n{format_name.upper()} Format:")
        logger.info(json.dumps(format_example, indent=2, ensure_ascii=False))

def explain_tsquery_syntax():
    """Explain the tsquery syntax and best practices."""
    
    logger.info("\n=== PostgreSQL tsquery Syntax Guide ===")
    
    syntax_examples = [
        {
            "description": "Basic AND operation",
            "example": "heart & failure",
            "explanation": "Finds documents containing both 'heart' AND 'failure'"
        },
        {
            "description": "OR operation with synonyms",
            "example": "(MI | \"myocardial infarction\")",
            "explanation": "Finds documents with either 'MI' OR 'myocardial infarction'"
        },
        {
            "description": "Complex grouping",
            "example": "(ONSD | \"optic nerve sheath diameter\") & ultrasound & (ICP | \"intracranial pressure\")",
            "explanation": "Combines multiple synonym groups with AND operations"
        },
        {
            "description": "Measurement terms",
            "example": "(cutoff | threshold | \"cut-off\") & sensitivity",
            "explanation": "Includes various ways to express measurement concepts"
        },
        {
            "description": "Avoiding over-specification",
            "example": "heart & failure",
            "explanation": "Better than 'acute heart failure' which would miss 'chronic heart failure'"
        }
    ]
    
    for example in syntax_examples:
        logger.info(f"\n{example['description']}:")
        logger.info(f"  Example: {example['example']}")
        logger.info(f"  Explanation: {example['explanation']}")

def show_medical_abbreviations():
    """Show the medical abbreviations and their expansions."""
    
    logger.info("\n=== Medical Abbreviations Handled ===")
    
    generator = TsqueryTrainingGenerator()
    
    # Show some key medical abbreviations
    key_abbreviations = {
        'MI': generator.medical_synonyms.get('MI', []),
        'ICP': generator.medical_synonyms.get('ICP', []),
        'ONSD': generator.medical_synonyms.get('ONSD', []),
        'CT': generator.medical_synonyms.get('CT', []),
        'MRI': generator.medical_synonyms.get('MRI', []),
        'ECG': generator.medical_synonyms.get('ECG', []),
        'CPR': generator.medical_synonyms.get('CPR', []),
        'ROSC': generator.medical_synonyms.get('ROSC', [])
    }
    
    for abbrev, expansions in key_abbreviations.items():
        if expansions:
            logger.info(f"{abbrev}: {', '.join(expansions)}")

def usage_recommendations():
    """Provide usage recommendations for the training data."""
    
    logger.info("\n=== Usage Recommendations ===")
    
    recommendations = [
        "1. Generate 1000-5000 training examples for effective fine-tuning",
        "2. Use phi3 format for MLX training on Apple Silicon",
        "3. Include diverse medical specialties in your Q&A source data",
        "4. Validate generated tsqueries against your actual database",
        "5. Consider manual review of a sample of generated queries",
        "6. Test the trained model on held-out medical questions",
        "7. Iterate on the synonym dictionaries based on your domain",
        "8. Monitor for overly complex queries that might hurt performance"
    ]
    
    for rec in recommendations:
        logger.info(rec)

if __name__ == "__main__":
    # Run the demonstration
    demonstrate_tsquery_generation()
    show_training_data_formats()
    explain_tsquery_syntax()
    show_medical_abbreviations()
    usage_recommendations()
    
    logger.info("\n=== Next Steps ===")
    logger.info("1. Run: python generate_tsquery_training_data.py --limit 100")
    logger.info("2. Fine-tune your model with the generated training data")
    logger.info("3. Test the model with: python postgres_query_tester.py")
    logger.info("4. Deploy the trained model for production use")
