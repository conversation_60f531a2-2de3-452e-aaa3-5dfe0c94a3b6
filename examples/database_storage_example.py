#!/usr/bin/env python3
"""
Example script demonstrating database storage functionality for Q&A generation.

This script shows how to:
1. Generate Q&A pairs from medical abstracts
2. Store them directly in the database
3. Query the stored data for verification

Prerequisites:
- PostgreSQL database with required tables
- Environment variables set for database connection
- Ollama running with mistral-small3.2 model
"""

import os
import sys
import json
import logging
from dotenv import load_dotenv

# Add the parent directory to the path so we can import abstract_qa
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from abstract_qa import AbstractQARetriever

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def demonstrate_database_storage():
    """Demonstrate the database storage functionality."""
    
    logger.info("=== Database Storage Example ===")
    
    # Initialize the retriever
    retriever = AbstractQARetriever(model_name="mistral-small3.2")
    
    try:
        # Connect to database
        retriever.connect()
        logger.info("✅ Connected to database")
        
        # Get a small sample of documents for demonstration
        logger.info("📚 Retrieving sample documents...")
        documents = retriever.get_2025_abstracts(limit=3)  # Just 3 documents for demo
        
        if not documents:
            logger.error("❌ No documents retrieved")
            return False
        
        logger.info(f"📄 Retrieved {len(documents)} documents")
        
        # Show sample document info
        for i, doc in enumerate(documents, 1):
            logger.info(f"  {i}. {doc['publication_date']} - {doc['title'][:80]}...")
        
        # Generate and store Q&A pairs in database
        logger.info("\n🤖 Generating Q&A pairs and storing in database...")
        stats = retriever.generate_qa_to_database(documents)
        
        # Display results
        logger.info("\n📊 Generation Results:")
        logger.info(f"  Total documents processed: {stats['processed_documents']}/{stats['total_documents']}")
        logger.info(f"  Q&A pairs generated: {stats['total_qa_pairs']}")
        logger.info(f"  Generation params ID: {stats['generation_params_id']}")
        
        if stats['failed_documents'] > 0:
            logger.warning(f"  Failed documents: {stats['failed_documents']}")
        if stats['skipped_documents'] > 0:
            logger.warning(f"  Skipped documents: {stats['skipped_documents']}")
        
        # Verify the data was stored by querying the database
        logger.info("\n🔍 Verifying stored data...")
        verify_stored_data(retriever, stats['generation_params_id'])
        
        logger.info("\n🎉 Database storage demonstration completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error during demonstration: {e}")
        return False
    finally:
        retriever.disconnect()

def verify_stored_data(retriever, generation_params_id):
    """Verify that data was properly stored in the database."""
    
    try:
        # Query the generated_data table
        query = """
            SELECT gd.id, gd.data, d.title, d.doi, d.external_id
            FROM generated_data gd
            JOIN document d ON gd.document_id = d.id
            WHERE gd.generation_params_id = %s
            ORDER BY gd.id DESC
            LIMIT 5
        """
        
        retriever.cursor.execute(query, (generation_params_id,))
        results = retriever.cursor.fetchall()
        
        if results:
            logger.info(f"✅ Found {len(results)} Q&A pairs in database:")
            
            for i, (qa_id, data_json, title, doi, external_id) in enumerate(results, 1):
                try:
                    qa_data = json.loads(data_json)
                    source = doi if doi else f"PMID:{external_id}" if external_id else "Unknown"
                    
                    logger.info(f"\n  Q&A Pair #{qa_id}:")
                    logger.info(f"    Source: {title[:60]}... ({source})")
                    logger.info(f"    Question: {qa_data.get('question', 'N/A')[:80]}...")
                    logger.info(f"    Answer: {qa_data.get('answer', 'N/A')[:80]}...")
                    
                except json.JSONDecodeError:
                    logger.warning(f"    Invalid JSON data for Q&A pair #{qa_id}")
        else:
            logger.warning("⚠️ No Q&A pairs found in database")
            
    except Exception as e:
        logger.error(f"❌ Error verifying stored data: {e}")

def show_generation_params(retriever, generation_params_id):
    """Show the generation parameters used."""
    
    try:
        query = """
            SELECT system_prompt, generation_prompt
            FROM generation_params
            WHERE id = %s
        """
        
        retriever.cursor.execute(query, (generation_params_id,))
        result = retriever.cursor.fetchone()
        
        if result:
            system_prompt, generation_prompt = result
            logger.info("\n📝 Generation Parameters:")
            logger.info(f"  System Prompt: {system_prompt[:100]}...")
            logger.info(f"  Generation Prompt: {generation_prompt[:100]}...")
        else:
            logger.warning("⚠️ Generation parameters not found")
            
    except Exception as e:
        logger.error(f"❌ Error retrieving generation parameters: {e}")

if __name__ == "__main__":
    logger.info("Starting database storage demonstration...")
    
    # Check environment variables
    required_vars = ['POSTGRES_HOST', 'POSTGRES_DB', 'POSTGRES_USER', 'POSTGRES_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        logger.info("Please set up your .env file with database connection details")
        sys.exit(1)
    
    # Run the demonstration
    success = demonstrate_database_storage()
    
    if success:
        logger.info("\n✨ Example completed successfully!")
        logger.info("\nNext steps:")
        logger.info("1. Check your database to see the stored Q&A pairs")
        logger.info("2. Use the data for machine learning training")
        logger.info("3. Run with more documents: python abstract_qa.py --limit 100 --to-database")
    else:
        logger.error("\n💥 Example failed!")
        sys.exit(1)
