#!/usr/bin/env python3
"""
Semantic Search Examples

This script demonstrates various types of semantic searches on medical literature
using the vector embedding database.
"""

import os
import sys
import logging

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from semantic_search import SemanticSearcher

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demonstrate_medical_searches():
    """Demonstrate semantic search with various medical queries."""
    
    logger.info("=== Medical Semantic Search Demonstration ===")
    
    # Example medical questions of different types
    medical_queries = [
        {
            "category": "Diagnostic",
            "questions": [
                "How is myocardial infarction diagnosed?",
                "What are the symptoms of stroke?",
                "Blood tests for diabetes diagnosis"
            ]
        },
        {
            "category": "Treatment",
            "questions": [
                "Treatment options for heart failure",
                "Antibiotic therapy for pneumonia",
                "Surgical management of appendicitis"
            ]
        },
        {
            "category": "Prognosis",
            "questions": [
                "Survival rates in lung cancer",
                "Recovery time after hip replacement",
                "Long-term outcomes of kidney transplant"
            ]
        },
        {
            "category": "Risk Factors",
            "questions": [
                "Risk factors for cardiovascular disease",
                "Smoking and cancer relationship",
                "Obesity complications and health risks"
            ]
        },
        {
            "category": "Pharmacology",
            "questions": [
                "Side effects of statins",
                "Drug interactions with warfarin",
                "Dosing guidelines for insulin"
            ]
        }
    ]
    
    searcher = SemanticSearcher()
    
    try:
        searcher.connect()
        
        # Get database stats first
        stats = searcher.get_database_stats()
        logger.info(f"Database contains {stats.get('total_documents', 0):,} documents with {stats.get('total_embeddings', 0):,} embeddings")
        
        for category_info in medical_queries:
            category = category_info["category"]
            questions = category_info["questions"]
            
            print(f"\n{'='*60}")
            print(f"CATEGORY: {category.upper()}")
            print(f"{'='*60}")
            
            for i, question in enumerate(questions, 1):
                print(f"\n{i}. Query: {question}")
                print("-" * 50)
                
                try:
                    # Generate embedding and search
                    embedding = searcher.get_question_embedding(question)
                    results = searcher.search_similar_chunks(embedding, limit=5)
                    
                    if results:
                        print(f"Found {len(results)} relevant documents:")
                        
                        for j, result in enumerate(results, 1):
                            similarity_pct = result['similarity_score'] * 100
                            print(f"  {j}. [{similarity_pct:5.1f}%] Doc {result['document_id']:4d}: {result['document_title'][:70]}...")
                            
                            # Show a snippet of the chunk text
                            if result['chunk_text']:
                                snippet = result['chunk_text'][:150].replace('\n', ' ')
                                print(f"      → {snippet}...")
                    else:
                        print("  No relevant documents found")
                        
                except Exception as e:
                    print(f"  Error: {e}")
        
    except Exception as e:
        logger.error(f"Demonstration failed: {e}")
    finally:
        searcher.disconnect()

def compare_search_strategies():
    """Compare different search strategies for the same question."""
    
    logger.info("\n=== Comparing Search Strategies ===")
    
    base_question = "heart failure"
    
    # Different ways to phrase the same medical concept
    search_variations = [
        "heart failure",
        "congestive heart failure",
        "cardiac insufficiency",
        "heart pump dysfunction",
        "reduced ejection fraction",
        "CHF treatment options",
        "What causes heart failure?",
        "How is heart failure diagnosed and treated?"
    ]
    
    searcher = SemanticSearcher()
    
    try:
        searcher.connect()
        
        print(f"\n{'='*80}")
        print("COMPARING SEARCH VARIATIONS FOR HEART FAILURE")
        print(f"{'='*80}")
        
        all_results = {}
        
        for variation in search_variations:
            print(f"\nQuery: '{variation}'")
            print("-" * 60)
            
            try:
                embedding = searcher.get_question_embedding(variation)
                results = searcher.search_similar_chunks(embedding, limit=3)
                
                all_results[variation] = results
                
                if results:
                    for i, result in enumerate(results, 1):
                        similarity_pct = result['similarity_score'] * 100
                        print(f"  {i}. [{similarity_pct:5.1f}%] Doc {result['document_id']}: {result['document_title'][:60]}...")
                else:
                    print("  No results found")
                    
            except Exception as e:
                print(f"  Error: {e}")
        
        # Analyze overlap in results
        print(f"\n{'='*60}")
        print("RESULT OVERLAP ANALYSIS")
        print(f"{'='*60}")
        
        # Find documents that appear in multiple searches
        doc_counts = {}
        for variation, results in all_results.items():
            for result in results:
                doc_id = result['document_id']
                if doc_id not in doc_counts:
                    doc_counts[doc_id] = {'count': 0, 'title': result['document_title'], 'variations': []}
                doc_counts[doc_id]['count'] += 1
                doc_counts[doc_id]['variations'].append(variation)
        
        # Show documents that appeared in multiple searches
        frequent_docs = {doc_id: info for doc_id, info in doc_counts.items() if info['count'] > 1}
        
        if frequent_docs:
            print("\nDocuments found by multiple search variations:")
            for doc_id, info in sorted(frequent_docs.items(), key=lambda x: x[1]['count'], reverse=True):
                print(f"  Doc {doc_id} ({info['count']} times): {info['title'][:60]}...")
                print(f"    Found by: {', '.join(info['variations'][:3])}{'...' if len(info['variations']) > 3 else ''}")
        else:
            print("\nNo documents appeared in multiple searches")
        
    except Exception as e:
        logger.error(f"Comparison failed: {e}")
    finally:
        searcher.disconnect()

def analyze_similarity_scores():
    """Analyze similarity score distributions for different types of queries."""
    
    logger.info("\n=== Similarity Score Analysis ===")
    
    test_queries = [
        ("Specific medical term", "myocardial infarction"),
        ("General concept", "heart disease"),
        ("Symptom description", "chest pain and shortness of breath"),
        ("Treatment query", "how to treat diabetes"),
        ("Very specific", "ONSD ultrasound measurement for intracranial pressure"),
        ("Broad topic", "medical research")
    ]
    
    searcher = SemanticSearcher()
    
    try:
        searcher.connect()
        
        print(f"\n{'='*80}")
        print("SIMILARITY SCORE ANALYSIS")
        print(f"{'='*80}")
        
        for query_type, query in test_queries:
            print(f"\n{query_type}: '{query}'")
            print("-" * 60)
            
            try:
                embedding = searcher.get_question_embedding(query)
                results = searcher.search_similar_chunks(embedding, limit=10)
                
                if results:
                    scores = [r['similarity_score'] for r in results]
                    
                    print(f"  Results: {len(results)}")
                    print(f"  Best score: {max(scores):.4f}")
                    print(f"  Worst score: {min(scores):.4f}")
                    print(f"  Average score: {sum(scores)/len(scores):.4f}")
                    print(f"  Score range: {max(scores) - min(scores):.4f}")
                    
                    # Show score distribution
                    high_scores = len([s for s in scores if s > 0.7])
                    medium_scores = len([s for s in scores if 0.4 <= s <= 0.7])
                    low_scores = len([s for s in scores if s < 0.4])
                    
                    print(f"  Score distribution: High(>0.7): {high_scores}, Medium(0.4-0.7): {medium_scores}, Low(<0.4): {low_scores}")
                else:
                    print("  No results found")
                    
            except Exception as e:
                print(f"  Error: {e}")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
    finally:
        searcher.disconnect()

def usage_examples():
    """Show practical usage examples."""
    
    print(f"\n{'='*80}")
    print("PRACTICAL USAGE EXAMPLES")
    print(f"{'='*80}")
    
    examples = [
        {
            "description": "Basic search",
            "command": "python semantic_search.py 'heart failure treatment'"
        },
        {
            "description": "Get more results",
            "command": "python semantic_search.py 'diabetes diagnosis' --limit 30"
        },
        {
            "description": "Show chunk text content",
            "command": "python semantic_search.py 'stroke symptoms' --show-text"
        },
        {
            "description": "Database statistics",
            "command": "python semantic_search.py 'any query' --stats"
        },
        {
            "description": "Use different embedding model",
            "command": "python semantic_search.py 'cancer treatment' --model 'different-model:latest'"
        },
        {
            "description": "Complex medical query",
            "command": "python semantic_search.py 'What are the risk factors for cardiovascular disease in diabetic patients?'"
        }
    ]
    
    for example in examples:
        print(f"\n{example['description']}:")
        print(f"  {example['command']}")

if __name__ == "__main__":
    print("🔍 Semantic Search Examples for Medical Literature")
    print("=" * 60)
    
    # Run demonstrations
    demonstrate_medical_searches()
    compare_search_strategies()
    analyze_similarity_scores()
    usage_examples()
    
    print(f"\n{'='*60}")
    print("💡 Tips for Better Semantic Search:")
    print("• Use natural language questions")
    print("• Include medical terminology when relevant")
    print("• Try different phrasings of the same concept")
    print("• Use --show-text to see chunk content")
    print("• Adjust --limit based on your needs")
    print("• Check similarity scores to gauge relevance")
