I have the followiuing tables:

document:
    id serial rimary key,
    title, abstract

chunk:
    document_id integer references document(id),
    title text,
    text text

emb_1024:
    id integer NOT NULL DEFAULT nextval('embedding_base_id_seq'::regclass),
    chunk_id integer references chunk(id)
    embedding vector(1024)

There is a hnsw index on embedding vector

create a python script that takes a human language question from the command line and does
a semantic (vector) search on the embedding in emb_1024, and listing the 20 highest scoring
titles and document_id's
The search question vector is created through the python ollama library using the
embedding model 'snowflake-arctic-embed2:latest'
