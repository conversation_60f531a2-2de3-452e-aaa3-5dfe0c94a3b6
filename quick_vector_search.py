#!/usr/bin/env python3
"""
Quick Vector Search - Optimized for Performance

This is a performance-optimized version of semantic search that minimizes
database operations and focuses on speed.
"""

import os
import sys
import logging
import argparse
from typing import List, Dict
import psycopg2
import psycopg2.extras
from psycopg2 import Error
from dotenv import load_dotenv
import ollama
import time

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class QuickVectorSearch:
    """Optimized vector search with minimal overhead."""

    def __init__(self, embedding_model: str = "snowflake-arctic-embed2:latest"):
        self.embedding_model = embedding_model
        self.connection = None
        self.cursor = None
        
        # Database connection
        self.db_config = {
            'host': os.getenv('POSTGRES_HOST'),
            'port': os.getenv('POSTGRES_PORT', 5432),
            'database': os.getenv('POSTGRES_DB'),
            'user': os.getenv('POSTGRES_USER'),
            'password': os.getenv('POSTGRES_PASSWORD')
        }

    def connect(self):
        """Establish database connection with optimizations."""
        try:
            # Connection with performance optimizations
            self.connection = psycopg2.connect(
                **self.db_config,
                # Performance settings
                cursor_factory=psycopg2.extras.RealDictCursor,
                options="-c default_transaction_isolation=read_committed"
            )
            self.cursor = self.connection.cursor()
            
            # Set session-level optimizations
            self.cursor.execute("SET work_mem = '256MB';")
            self.cursor.execute("SET random_page_cost = 1.1;")  # For SSD
            
        except Error as e:
            logger.error(f"Connection error: {e}")
            raise

    def disconnect(self):
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()

    def get_embedding(self, text: str) -> List[float]:
        """Get embedding with minimal overhead."""
        try:
            response = ollama.embeddings(model=self.embedding_model, prompt=text)
            return response['embedding']
        except Exception as e:
            logger.error(f"Embedding error: {e}")
            raise

    def search_fast(self, question: str, limit: int = 20) -> List[Dict]:
        """Optimized search with two-stage approach."""
        
        # Stage 1: Fast vector-only search
        start_time = time.time()
        embedding = self.get_embedding(question)
        embedding_time = time.time() - start_time
        
        embedding_str = '[' + ','.join(map(str, embedding)) + ']'
        
        # Stage 1: Get top chunk_ids quickly (vector search only)
        vector_start = time.time()
        vector_query = """
            SELECT chunk_id, embedding <=> %s::vector as distance
            FROM emb_1024
            ORDER BY embedding <=> %s::vector
            LIMIT %s;
        """
        
        self.cursor.execute(vector_query, (embedding_str, embedding_str, limit))
        vector_results = self.cursor.fetchall()
        vector_time = time.time() - vector_start
        
        if not vector_results:
            return []
        
        # Stage 2: Get metadata for top results (single join query)
        metadata_start = time.time()
        chunk_ids = [row['chunk_id'] for row in vector_results]
        distance_map = {row['chunk_id']: row['distance'] for row in vector_results}
        
        # Use ANY() for efficient IN clause with large lists
        metadata_query = """
            SELECT 
                c.id as chunk_id,
                c.document_id,
                d.title as document_title,
                c.text as chunk_text
            FROM chunks c
            JOIN document d ON c.document_id = d.id
            WHERE c.id = ANY(%s);
        """
        
        self.cursor.execute(metadata_query, (chunk_ids,))
        metadata_results = self.cursor.fetchall()
        metadata_time = time.time() - metadata_start
        
        # Combine results and maintain order
        final_results = []
        metadata_dict = {row['chunk_id']: row for row in metadata_results}
        
        for chunk_id in chunk_ids:
            if chunk_id in metadata_dict:
                result = metadata_dict[chunk_id]
                distance = distance_map[chunk_id]
                
                final_results.append({
                    'document_id': result['document_id'],
                    'document_title': result['document_title'],
                    'chunk_text': result['chunk_text'],
                    'distance': float(distance),
                    'similarity_score': float(1 - distance)
                })
        
        total_time = time.time() - start_time
        
        logger.info(f"Search completed in {total_time:.3f}s "
                   f"(embedding: {embedding_time:.3f}s, vector: {vector_time:.3f}s, metadata: {metadata_time:.3f}s)")
        
        return final_results

    def search_embedding_only(self, question: str, limit: int = 20) -> List[Dict]:
        """Ultra-fast embedding-only search (no metadata joins)."""
        
        start_time = time.time()
        embedding = self.get_embedding(question)
        embedding_str = '[' + ','.join(map(str, embedding)) + ']'
        
        query = """
            SELECT 
                chunk_id,
                embedding <=> %s::vector as distance,
                1 - (embedding <=> %s::vector) as similarity_score
            FROM emb_1024
            ORDER BY embedding <=> %s::vector
            LIMIT %s;
        """
        
        self.cursor.execute(query, (embedding_str, embedding_str, embedding_str, limit))
        results = self.cursor.fetchall()
        
        search_time = time.time() - start_time
        logger.info(f"Embedding-only search: {search_time:.3f}s")
        
        return [dict(row) for row in results]

def main():
    parser = argparse.ArgumentParser(description='Quick optimized vector search')
    parser.add_argument('question', type=str, help='Search question')
    parser.add_argument('--limit', type=int, default=20, help='Number of results')
    parser.add_argument('--fast', action='store_true', help='Use two-stage optimized search')
    parser.add_argument('--embedding-only', action='store_true', help='Search embeddings only (no metadata)')
    parser.add_argument('--benchmark', action='store_true', help='Compare different search methods')
    
    args = parser.parse_args()
    
    searcher = QuickVectorSearch()
    
    try:
        searcher.connect()
        
        if args.benchmark:
            # Benchmark different approaches
            print(f"\n{'='*60}")
            print("PERFORMANCE BENCHMARK")
            print(f"{'='*60}")
            print(f"Question: {args.question}")
            print(f"Limit: {args.limit}")
            
            # Test embedding-only search
            print(f"\n1. Embedding-only search:")
            start = time.time()
            embedding_results = searcher.search_embedding_only(args.question, args.limit)
            embedding_time = time.time() - start
            print(f"   Results: {len(embedding_results)}")
            print(f"   Time: {embedding_time:.3f}s")
            
            # Test two-stage search
            print(f"\n2. Two-stage optimized search:")
            start = time.time()
            fast_results = searcher.search_fast(args.question, args.limit)
            fast_time = time.time() - start
            print(f"   Results: {len(fast_results)}")
            print(f"   Time: {fast_time:.3f}s")
            
            print(f"\nSpeedup: {fast_time/embedding_time:.1f}x slower than embedding-only")
            
        elif args.embedding_only:
            # Embedding-only search
            results = searcher.search_embedding_only(args.question, args.limit)
            
            print(f"\n{'='*60}")
            print(f"EMBEDDING-ONLY SEARCH RESULTS ({len(results)})")
            print(f"{'='*60}")
            
            for i, result in enumerate(results, 1):
                print(f"{i:2d}. Chunk ID: {result['chunk_id']} | "
                      f"Similarity: {result['similarity_score']:.4f}")
            
        else:
            # Full search (default or --fast)
            if args.fast:
                results = searcher.search_fast(args.question, args.limit)
            else:
                # Use the optimized search by default
                results = searcher.search_fast(args.question, args.limit)
            
            print(f"\n{'='*60}")
            print(f"SEARCH RESULTS ({len(results)})")
            print(f"{'='*60}")
            
            for i, result in enumerate(results, 1):
                print(f"\n{i:2d}. Document ID: {result['document_id']} | "
                      f"Similarity: {result['similarity_score']:.4f}")
                print(f"    Title: {result['document_title']}")
                
                # Show text preview
                if result['chunk_text']:
                    preview = result['chunk_text'][:150].replace('\n', ' ')
                    print(f"    Preview: {preview}...")
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        sys.exit(1)
    finally:
        searcher.disconnect()

if __name__ == "__main__":
    main()
